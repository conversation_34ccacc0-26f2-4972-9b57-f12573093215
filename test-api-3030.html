<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试 - 端口3030</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API测试 - 端口3030</h1>
        
        <div class="test-section">
            <h3>🔑 Token管理</h3>
            <div class="form-group">
                <label for="token">JWT Token:</label>
                <textarea id="token" rows="3" placeholder="请输入JWT token"></textarea>
            </div>
            <button onclick="setToken()">设置Token</button>
            <button onclick="clearToken()">清除Token</button>
            <div id="token-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>👤 获取用户信息</h3>
            <button onclick="testGetUserInfo()">GET /api/members/me</button>
            <div id="userinfo-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>📝 更新用户昵称</h3>
            <div class="form-group">
                <label for="nickname">新昵称:</label>
                <input type="text" id="nickname" placeholder="请输入新昵称" maxlength="20">
            </div>
            <button onclick="testUpdateNickname()">PUT /api/members/me</button>
            <div id="nickname-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>🖼️ 头像上传</h3>
            <div class="form-group">
                <label for="avatar">选择头像文件:</label>
                <input type="file" id="avatar" accept="image/*">
            </div>
            <button onclick="testUploadAvatar()">POST /api/members/upload-avatar</button>
            <div id="avatar-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3030';
        
        // 设置Token
        function setToken() {
            const token = document.getElementById('token').value.trim();
            if (token) {
                localStorage.setItem('token', token);
                showResult('token-result', 'Token已设置', 'success');
            } else {
                showResult('token-result', '请输入Token', 'error');
            }
        }
        
        // 清除Token
        function clearToken() {
            localStorage.removeItem('token');
            showResult('token-result', 'Token已清除', 'success');
        }
        
        // 获取Token
        function getToken() {
            return localStorage.getItem('token');
        }
        
        // 显示结果
        function showResult(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
        
        // 测试获取用户信息
        async function testGetUserInfo() {
            const token = getToken();
            
            if (!token) {
                showResult('userinfo-result', '请先设置Token', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/members/me`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('userinfo-result', `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('userinfo-result', `错误: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('userinfo-result', `请求失败: ${error.message}`, 'error');
            }
        }
        
        // 测试更新昵称
        async function testUpdateNickname() {
            const nickname = document.getElementById('nickname').value.trim();
            const token = getToken();
            
            if (!token) {
                showResult('nickname-result', '请先设置Token', 'error');
                return;
            }
            
            if (!nickname) {
                showResult('nickname-result', '请输入昵称', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/members/me`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ name: nickname })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('nickname-result', `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('nickname-result', `错误: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('nickname-result', `请求失败: ${error.message}`, 'error');
            }
        }
        
        // 测试上传头像
        async function testUploadAvatar() {
            const fileInput = document.getElementById('avatar');
            const token = getToken();
            
            if (!token) {
                showResult('avatar-result', '请先设置Token', 'error');
                return;
            }
            
            if (!fileInput.files[0]) {
                showResult('avatar-result', '请选择头像文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('avatar', fileInput.files[0]);
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/members/upload-avatar`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('avatar-result', `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('avatar-result', `错误: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('avatar-result', `请求失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时检查Token
        window.onload = function() {
            const token = getToken();
            if (token) {
                document.getElementById('token').value = token;
                showResult('token-result', 'Token已加载', 'success');
            }
        };
    </script>
</body>
</html>
