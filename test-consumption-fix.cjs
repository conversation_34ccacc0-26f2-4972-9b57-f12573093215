const mysql = require('mysql2/promise');

async function testConsumptionFix() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });
    
    console.log('✅ 数据库连接成功\n');
    
    // 1. 查看agent_theme表中的消耗配置
    console.log('=== agent_theme表中的消耗配置 ===');
    const [themes] = await connection.execute(`
      SELECT id, title, type, consumption, workflowId, agentId, enabled 
      FROM agent_theme 
      WHERE enabled = 1 
      ORDER BY id DESC 
      LIMIT 10
    `);
    
    console.log('启用的智能体和工作流配置:');
    themes.forEach(theme => {
      console.log(`ID: ${theme.id}, 标题: ${theme.title}, 类型: ${theme.type}`);
      console.log(`配置消耗: ${theme.consumption} 点`);
      console.log(`WorkflowID: ${theme.workflowId || '无'}, AgentID: ${theme.agentId || '无'}`);
      console.log('---');
    });
    
    // 2. 查看最近的task记录，看消耗数据格式
    console.log('\n=== 最近的task记录消耗数据 ===');
    const [tasks] = await connection.execute(`
      SELECT id, userName, taskType, taskTypeDetail, consumption, status, createTime 
      FROM task 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    tasks.forEach(task => {
      console.log(`ID: ${task.id}, 用户: ${task.userName}, 类型: ${task.taskType}`);
      console.log(`详情: ${task.taskTypeDetail}`);
      
      // 解析consumption数据
      let consumptionData;
      try {
        if (typeof task.consumption === 'string') {
          consumptionData = JSON.parse(task.consumption);
        } else {
          consumptionData = task.consumption;
        }
        
        if (typeof consumptionData === 'object') {
          console.log(`消耗详情: tokens=${consumptionData.tokens}, inputToken=${consumptionData.inputToken || 0}, outputToken=${consumptionData.outputToken || 0}`);
          if (consumptionData.workflowId) {
            console.log(`工作流ID: ${consumptionData.workflowId}`);
          }
          if (consumptionData.botId) {
            console.log(`智能体ID: ${consumptionData.botId}`);
          }
        } else {
          console.log(`消耗: ${consumptionData} 点`);
        }
      } catch (e) {
        console.log(`消耗: ${task.consumption} (解析失败)`);
      }
      
      console.log(`状态: ${task.status}, 创建时间: ${task.createTime}`);
      console.log('---');
    });
    
    // 3. 模拟测试数据一致性
    console.log('\n=== 数据一致性检查 ===');
    
    // 检查工作流数据一致性
    const [workflowTasks] = await connection.execute(`
      SELECT t.id, t.consumption, at.consumption as theme_consumption, at.title
      FROM task t
      LEFT JOIN agent_theme at ON JSON_EXTRACT(t.consumption, '$.workflowId') = at.workflowId
      WHERE t.taskType = '工作流' AND at.enabled = 1
      ORDER BY t.id DESC
      LIMIT 3
    `);
    
    console.log('工作流数据一致性检查:');
    workflowTasks.forEach(task => {
      let taskConsumption;
      try {
        const consumptionData = typeof task.consumption === 'string' 
          ? JSON.parse(task.consumption) 
          : task.consumption;
        taskConsumption = consumptionData.tokens || consumptionData;
      } catch (e) {
        taskConsumption = task.consumption;
      }
      
      const isConsistent = taskConsumption == task.theme_consumption;
      console.log(`任务ID: ${task.id}, 工作流: ${task.title}`);
      console.log(`任务记录消耗: ${taskConsumption}, 配置消耗: ${task.theme_consumption}, 一致性: ${isConsistent ? '✅' : '❌'}`);
    });
    
    // 检查智能体数据一致性
    const [agentTasks] = await connection.execute(`
      SELECT t.id, t.consumption, at.consumption as theme_consumption, at.title
      FROM task t
      LEFT JOIN agent_theme at ON JSON_EXTRACT(t.consumption, '$.botId') = at.agentId
      WHERE t.taskType = '智能体' AND at.enabled = 1
      ORDER BY t.id DESC
      LIMIT 3
    `);
    
    console.log('\n智能体数据一致性检查:');
    agentTasks.forEach(task => {
      let taskConsumption;
      try {
        const consumptionData = typeof task.consumption === 'string' 
          ? JSON.parse(task.consumption) 
          : task.consumption;
        taskConsumption = consumptionData.tokens || consumptionData;
      } catch (e) {
        taskConsumption = task.consumption;
      }
      
      const isConsistent = taskConsumption == task.theme_consumption;
      console.log(`任务ID: ${task.id}, 智能体: ${task.title}`);
      console.log(`任务记录消耗: ${taskConsumption}, 配置消耗: ${task.theme_consumption}, 一致性: ${isConsistent ? '✅' : '❌'}`);
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

testConsumptionFix();
