<template>
	<view v-if="visible" class="modal-overlay" @click="closeModal">
		<view class="modal-card" @click.stop>
			<view class="modal-header">
				<text class="modal-title">个人设置</text>
				<view class="modal-close" @click="closeModal">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<view class="modal-content">
				<!-- 头像设置 -->
				<view class="setting-item">
					<view class="setting-label">头像</view>
					<view class="setting-value">
						<view class="avatar-container" @click="changeAvatar">
							<image 
								:src="userInfo.avatar || '/static/images/default-avatar.png'" 
								class="avatar-image"
								mode="aspectFill"
							/>
							<view class="avatar-overlay">
								<text class="camera-icon">📷</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 昵称设置 -->
				<view class="setting-item">
					<view class="setting-label">昵称</view>
					<view class="setting-value">
						<!-- 方案1: textarea -->
						<textarea
							v-model="formData.nickname"
							class="setting-input setting-textarea"
							placeholder="请输入昵称"
							maxlength="20"
							:auto-height="false"
							:show-confirm-bar="false"
							:cursor-spacing="0"
							@input="handleNicknameInput"
							@focus="handleFocus"
							@blur="handleBlur"
						></textarea>

						<!-- 方案2: 可编辑div (备用) -->
						<!-- <view
							class="setting-input setting-editable"
							:class="{ 'has-content': formData.nickname }"
							@click="focusEditableDiv"
						>
							<text v-if="!formData.nickname" class="placeholder-text">请输入昵称</text>
							<text v-else>{{ formData.nickname }}</text>
						</view> -->
					</view>
				</view>
			</view>

			<view class="modal-footer">
				<button class="btn btn-cancel" @click="closeModal">取消</button>
				<button class="btn btn-primary" @click="saveSettings" :disabled="saving">
					{{ saving ? '保存中...' : '保存' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import memberAPI from '@/api/members.js'
import { API_BASE_URL } from '@/config/index.js'

export default {
	name: 'PersonalSettingsModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		userInfo: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			saving: false,
			API_BASE_URL,
			formData: {
				nickname: ''
			}
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.initFormData()
			}
		},
		userInfo: {
			handler() {
				this.initFormData()
			},
			deep: true
		}
	},
	methods: {
		// 初始化表单数据
		initFormData() {
			console.log('🚀 开始初始化表单数据')
			console.log('🚀 用户信息:', this.userInfo)

			const nickname = this.userInfo.name || this.userInfo.nickname || ''
			console.log('🚀 提取的昵称:', nickname)

			this.formData = {
				nickname: nickname
			}

			console.log('🚀 初始化后的表单数据:', this.formData)

			// 强制更新视图
			this.$nextTick(() => {
				this.$forceUpdate()
				console.log('🚀 强制更新完成')
			})
		},

		// 处理昵称输入
		handleNicknameInput(e) {
			console.log('🎯 昵称输入事件触发:', e)
			console.log('🎯 输入值:', e.detail ? e.detail.value : e.target ? e.target.value : 'unknown')

			const value = e.detail ? e.detail.value : (e.target ? e.target.value : '')
			console.log('🎯 最终值:', value)

			// 直接设置值
			this.formData.nickname = value

			console.log('🎯 更新后的formData:', this.formData)
		},

		// 处理焦点事件
		handleFocus(e) {
			console.log('🎯 输入框获得焦点')
		},

		// 处理失焦事件
		handleBlur(e) {
			console.log('🎯 输入框失去焦点')
		},

		// 关闭弹窗
		closeModal() {
			this.$emit('close')
		},

		// 更换头像
		changeAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0]
					this.uploadAvatar(tempFilePath)
				},
				fail: (err) => {
					console.error('选择图片失败:', err)
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					})
				}
			})
		},

		// 上传头像
		async uploadAvatar(filePath) {
			try {
				uni.showLoading({ title: '上传中...' })

				// 使用文件上传接口
				const uploadResult = await new Promise((resolve, reject) => {
					uni.uploadFile({
						url: this.API_BASE_URL + '/api/members/upload-avatar',
						filePath: filePath,
						name: 'avatar',
						header: {
							'Authorization': 'Bearer ' + uni.getStorageSync('token')
						},
						success: (res) => {
							try {
								const data = JSON.parse(res.data);
								if (data.success) {
									resolve(data);
								} else {
									reject(new Error(data.message || '上传失败'));
								}
							} catch (e) {
								reject(new Error('响应解析失败'));
							}
						},
						fail: (err) => {
							reject(new Error(err.errMsg || '上传失败'));
						}
					});
				});

				uni.hideLoading()
				uni.showToast({
					title: '头像更新成功',
					icon: 'success'
				})

				// 发送新的头像URL给父组件
				this.$emit('avatar-updated', uploadResult.data.avatarUrl)
			} catch (error) {
				uni.hideLoading()
				console.error('上传头像失败:', error)
				uni.showToast({
					title: error.message || '上传失败',
					icon: 'none'
				})
			}
		},

		// 保存设置
		async saveSettings() {
			console.log('开始保存设置')
			console.log('当前表单数据:', this.formData)
			try {
				// 验证昵称
				if (!this.formData.nickname.trim()) {
					console.log('昵称为空，显示提示')
					uni.showToast({
						title: '请输入昵称',
						icon: 'none'
					})
					return
				}

				if (this.formData.nickname.trim().length > 20) {
					uni.showToast({
						title: '昵称不能超过20个字符',
						icon: 'none'
					})
					return
				}

				this.saving = true

				// 调用API更新用户信息
				const updateData = {
					name: this.formData.nickname.trim()
				}

				await memberAPI.updateCurrentUser(updateData)

				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})

				// 通知父组件更新用户信息
				this.$emit('settings-updated', updateData)
				this.closeModal()

			} catch (error) {
				console.error('保存设置失败:', error)
				uni.showToast({
					title: error.message || '保存失败',
					icon: 'none'
				})
			} finally {
				this.saving = false
			}
		}
	}
}
</script>

<style scoped>
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	backdrop-filter: blur(10px);
}

.modal-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20px;
	width: 350px;
	max-width: 90%;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: scale(0.8) translateY(20px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
	color: white;
	font-size: 18px;
	font-weight: bold;
}

.modal-close {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.close-icon {
	color: white;
	font-size: 16px;
}

.modal-content {
	padding: 20px;
	max-height: 400px;
	overflow-y: auto;
}

.setting-item {
	margin-bottom: 20px;
}

.setting-label {
	color: rgba(255, 255, 255, 0.9);
	font-size: 14px;
	margin-bottom: 8px;
	font-weight: 500;
}

.setting-value {
	position: relative;
}

.setting-input {
	width: 100%;
	padding: 15px 18px;
	border: 2px solid rgba(255, 255, 255, 0.2);
	border-radius: 12px;
	background: rgba(255, 255, 255, 0.1);
	color: white;
	font-size: 15px;
	box-sizing: border-box;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
}

.setting-textarea {
	min-height: 50px;
	max-height: 50px;
	resize: none;
	line-height: 1.4;
	font-family: inherit;
}

.setting-input:focus {
	border-color: rgba(255, 255, 255, 0.5);
	background: rgba(255, 255, 255, 0.15);
	outline: none;
	box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
	transform: translateY(-1px);
}

.setting-input:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.setting-input::placeholder {
	color: rgba(255, 255, 255, 0.6);
	font-size: 14px;
}

.setting-note {
	position: absolute;
	right: 15px;
	top: 50%;
	transform: translateY(-50%);
	color: rgba(255, 255, 255, 0.6);
	font-size: 12px;
}

.avatar-container {
	position: relative;
	width: 90px;
	height: 90px;
	border-radius: 50%;
	overflow: hidden;
	cursor: pointer;
	border: 3px solid rgba(255, 255, 255, 0.3);
	transition: all 0.3s ease;
	box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.avatar-container:hover {
	transform: scale(1.05);
	border-color: rgba(255, 255, 255, 0.6);
	box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	object-fit: cover;
}

.avatar-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: all 0.3s ease;
	backdrop-filter: blur(5px);
}

.avatar-container:hover .avatar-overlay {
	opacity: 1;
}

.camera-icon {
	font-size: 28px;
	color: white;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.modal-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
	padding: 20px;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
	padding: 12px 24px;
	border-radius: 10px;
	border: none;
	font-size: 15px;
	cursor: pointer;
	transition: all 0.3s ease;
	font-weight: 500;
	min-width: 80px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.btn-cancel {
	background: rgba(255, 255, 255, 0.15);
	color: white;
	border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-cancel:hover {
	background: rgba(255, 255, 255, 0.25);
}

.btn-primary {
	background: linear-gradient(45deg, #ff6b6b, #feca57);
	color: white;
	font-weight: bold;
	position: relative;
	overflow: hidden;
}

.btn-primary::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s;
}

.btn-primary:hover::before {
	left: 100%;
}

.btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
	transform: none !important;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}
</style>
