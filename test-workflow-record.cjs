const mysql = require('mysql2/promise');

async function testWorkflowRecord() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });
    
    console.log('✅ 数据库连接成功\n');
    
    // 模拟新的工作流记录格式
    const testWorkflowData = {
      userId: 59,
      userName: '测试用户',
      taskSource: '/static/images/workflow-test.png', // 工作流图标
      taskType: '工作流',
      taskTypeDetail: '测试工作流',
      consumption: JSON.stringify({
        amount: 15,
        tokens: 15,
        inputToken: 0,
        outputToken: 0,
        workflowId: 'test_workflow_123',
        workflowTitle: '测试工作流'
      }),
      status: '已完成',
      logs: '测试工作流执行成功',
      input: 'test input',
      output: 'test output',
      instructions: '执行测试工作流'
    };
    
    console.log('🧪 插入测试工作流记录...');
    const insertResult = await connection.query(`
      INSERT INTO task (
        userId, userName, taskSource, taskType, taskTypeDetail,
        consumption, status, logs, input, output, instructions,
        createTime, updateTime
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      testWorkflowData.userId,
      testWorkflowData.userName,
      testWorkflowData.taskSource,
      testWorkflowData.taskType,
      testWorkflowData.taskTypeDetail,
      testWorkflowData.consumption,
      testWorkflowData.status,
      testWorkflowData.logs,
      testWorkflowData.input,
      testWorkflowData.output,
      testWorkflowData.instructions
    ]);
    
    console.log('✅ 测试记录插入成功，ID:', insertResult[0].insertId);
    
    // 查询刚插入的记录
    const [newRecord] = await connection.query(`
      SELECT id, taskType, taskTypeDetail, taskSource, consumption
      FROM task 
      WHERE id = ?
    `, [insertResult[0].insertId]);
    
    if (newRecord.length > 0) {
      const record = newRecord[0];
      console.log('\n📋 新插入的记录详情:');
      console.log(`   ID: ${record.id}`);
      console.log(`   类型: ${record.taskType}`);
      console.log(`   详情: ${record.taskTypeDetail}`);
      console.log(`   taskSource (图标): ${record.taskSource}`);
      console.log(`   consumption: ${typeof record.consumption === 'object' ? JSON.stringify(record.consumption) : record.consumption}`);
      
      // 测试前端解析逻辑
      let consumptionData;
      try {
        consumptionData = typeof record.consumption === 'string' ? 
          JSON.parse(record.consumption) : record.consumption;
      } catch (e) {
        consumptionData = record.consumption;
      }
      
      const amount = consumptionData?.amount || consumptionData?.tokens || consumptionData?.consumption || 0;
      console.log(`   前端解析的消耗点数: ${amount}`);
    }
    
    // 模拟新的智能体记录格式
    const testAgentData = {
      userId: 59,
      userName: '测试用户',
      taskSource: '/static/images/test-agent.png', // 智能体图标
      taskType: '智能体',
      taskTypeDetail: '测试智能体',
      consumption: JSON.stringify({
        amount: 500,
        tokens: 500,
        inputToken: 10,
        outputToken: 100,
        botId: 'test_bot_123',
        agentName: '测试智能体',
        agentAvatar: '/static/images/test-agent.png'
      }),
      status: '已完成',
      logs: '测试智能体对话成功',
      input: 'test question',
      output: 'test answer',
      instructions: '与测试智能体对话'
    };
    
    console.log('\n🤖 插入测试智能体记录...');
    const agentInsertResult = await connection.query(`
      INSERT INTO task (
        userId, userName, taskSource, taskType, taskTypeDetail,
        consumption, status, logs, input, output, instructions,
        createTime, updateTime
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      testAgentData.userId,
      testAgentData.userName,
      testAgentData.taskSource,
      testAgentData.taskType,
      testAgentData.taskTypeDetail,
      testAgentData.consumption,
      testAgentData.status,
      testAgentData.logs,
      testAgentData.input,
      testAgentData.output,
      testAgentData.instructions
    ]);
    
    console.log('✅ 测试智能体记录插入成功，ID:', agentInsertResult[0].insertId);
    
    // 查询刚插入的智能体记录
    const [newAgentRecord] = await connection.query(`
      SELECT id, taskType, taskTypeDetail, taskSource, consumption
      FROM task 
      WHERE id = ?
    `, [agentInsertResult[0].insertId]);
    
    if (newAgentRecord.length > 0) {
      const record = newAgentRecord[0];
      console.log('\n🤖 新插入的智能体记录详情:');
      console.log(`   ID: ${record.id}`);
      console.log(`   类型: ${record.taskType}`);
      console.log(`   详情: ${record.taskTypeDetail}`);
      console.log(`   taskSource (图标): ${record.taskSource}`);
      console.log(`   consumption: ${typeof record.consumption === 'object' ? JSON.stringify(record.consumption) : record.consumption}`);
      
      // 测试前端解析逻辑
      let consumptionData;
      try {
        consumptionData = typeof record.consumption === 'string' ? 
          JSON.parse(record.consumption) : record.consumption;
      } catch (e) {
        consumptionData = record.consumption;
      }
      
      const amount = consumptionData?.amount || consumptionData?.tokens || consumptionData?.consumption || 0;
      console.log(`   前端解析的消耗点数: ${amount}`);
    }

  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ 数据库连接已关闭');
    }
  }
}

testWorkflowRecord();
