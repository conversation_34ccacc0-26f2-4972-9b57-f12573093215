<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简洁居中弹窗演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-button {
            background: white;
            color: #667eea;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(255, 255, 255, 0.4);
        }

        /* 弹窗遮罩 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* 套餐详情弹窗样式 - 简洁居中版本 */
        .package-detail-modal {
            width: 600px;
            max-width: 80vw;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            position: relative;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            0% {
                opacity: 0;
                transform: scale(0.9) translateY(30px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* 弹窗头部样式 - 简洁版本 */
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .modal-title {
            font-size: 36px;
            font-weight: 600;
            color: white;
            text-align: center;
            flex: 1;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .close-btn:active {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0.95);
        }

        .close-icon {
            font-size: 32px;
            color: white;
            font-weight: 300;
        }

        /* 弹窗内容样式 - 简洁居中版本 */
        .detail-content {
            padding: 40px 32px;
            text-align: center;
        }

        /* 成功提示区域 */
        .success-section {
            margin-bottom: 40px;
        }

        .success-icon-large {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .success-check {
            font-size: 60px;
            color: white;
            font-weight: bold;
        }

        .success-text {
            font-size: 32px;
            color: #333;
            font-weight: 500;
        }

        /* 套餐信息区域 */
        .package-info-section {
            background: #f8f9ff;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .package-title-row {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
            margin-bottom: 12px;
        }

        .package-name {
            font-size: 32px;
            font-weight: 600;
            color: #333;
        }

        .active-badge {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 24px;
            font-weight: 500;
        }

        .badge-text {
            color: white;
        }

        .package-desc {
            font-size: 28px;
            color: #666;
            line-height: 1.4;
        }

        /* 信息网格样式 */
        .info-grid {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid #f0f0f0;
        }

        .info-row {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-item {
            flex: 1;
            padding: 24px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            border-right: 1px solid #f0f0f0;
        }

        .info-item:last-child {
            border-right: none;
        }

        .info-label {
            font-size: 24px;
            color: #999;
            margin-bottom: 8px;
        }

        .info-value {
            font-size: 28px;
            color: #333;
            font-weight: 500;
        }

        .info-value.highlight {
            color: #667eea;
            font-weight: 600;
        }

        .info-value.success {
            color: #4CAF50;
            font-weight: 600;
        }

        /* 弹窗底部 - 简洁版本 */
        .detail-footer {
            padding: 32px;
            display: flex;
            justify-content: center;
            background: white;
        }

        .detail-footer .confirm-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 24px 60px;
            border-radius: 50px;
            font-size: 32px;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .detail-footer .confirm-button:active {
            transform: translateY(2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .detail-footer .button-text {
            color: white;
        }

        /* 响应式适配 */
        @media screen and (max-width: 768px) {
            .package-detail-modal {
                width: 95vw;
                max-width: 550px;
                max-height: 85vh;
            }

            .modal-title {
                font-size: 32px;
            }

            .success-icon-large {
                width: 100px;
                height: 100px;
            }

            .success-check {
                font-size: 50px;
            }

            .success-text {
                font-size: 28px;
            }

            .package-name {
                font-size: 28px;
            }

            .package-desc {
                font-size: 24px;
            }

            .info-item {
                padding: 20px 16px;
            }

            .info-label {
                font-size: 22px;
            }

            .info-value {
                font-size: 24px;
            }

            .detail-footer .confirm-button {
                padding: 20px 50px;
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <button class="demo-button" onclick="showModal()">
        🎯 查看简洁居中弹窗
    </button>

    <!-- 简洁版套餐详情弹窗 -->
    <div id="packageModal" class="modal-overlay" onclick="closeModal()">
        <div class="package-detail-modal" onclick="event.stopPropagation()">
            <!-- 弹窗头部 -->
            <div class="modal-header">
                <span class="modal-title">套餐激活成功</span>
                <button class="close-btn" onclick="closeModal()">
                    <span class="close-icon">×</span>
                </button>
            </div>

            <!-- 弹窗内容 -->
            <div class="detail-content">
                <!-- 成功图标 -->
                <div class="success-section">
                    <div class="success-icon-large">
                        <span class="success-check">✓</span>
                    </div>
                    <span class="success-text">恭喜您！套餐已成功激活</span>
                </div>

                <!-- 套餐信息 -->
                <div class="package-info-section">
                    <div class="package-title-row">
                        <span class="package-name">VIP套餐</span>
                        <div class="active-badge">
                            <span class="badge-text">已激活</span>
                        </div>
                    </div>
                    <span class="package-desc">高级会员套餐，享受更多特权服务</span>
                </div>

                <!-- 详细信息 -->
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">开通时间</span>
                            <span class="info-value">2025-07-30 19:41</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">到期时间</span>
                            <span class="info-value">2025-08-30 19:41</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">有效期</span>
                            <span class="info-value highlight">30 天</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">获得点数</span>
                            <span class="info-value success">+1000</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">当前余额</span>
                            <span class="info-value">¥50.0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">当前点数</span>
                            <span class="info-value">1500</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 弹窗底部 -->
            <div class="detail-footer">
                <button class="confirm-button" onclick="closeModal()">
                    <span class="button-text">确定</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showModal() {
            const modal = document.getElementById('packageModal');
            modal.classList.add('show');
        }

        function closeModal() {
            const modal = document.getElementById('packageModal');
            modal.classList.remove('show');
        }

        // 按ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
