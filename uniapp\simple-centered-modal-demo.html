<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简洁居中弹窗演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-button {
            background: white;
            color: #667eea;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(255, 255, 255, 0.4);
        }

        /* 弹窗遮罩 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* 套餐详情弹窗样式 - 紧凑版本 */
        .package-detail-modal {
            width: 520px;
            max-width: 75vw;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
            position: relative;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            0% {
                opacity: 0;
                transform: scale(0.9) translateY(30px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* 弹窗头部样式 - 紧凑版本 */
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 28px 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .modal-title {
            font-size: 30px;
            font-weight: 600;
            color: white;
            text-align: center;
            flex: 1;
        }

        .close-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .close-btn:active {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0.95);
        }

        .close-icon {
            font-size: 28px;
            color: white;
            font-weight: 300;
        }

        /* 弹窗内容样式 - 紧凑版本 */
        .detail-content {
            padding: 24px;
        }

        /* 紧凑套餐信息 */
        .package-info-compact {
            background: #f8f9ff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .package-title-compact {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .package-name-compact {
            font-size: 28px;
            font-weight: 600;
            color: #333;
        }

        .active-badge-compact {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 20px;
            font-weight: 500;
        }

        .badge-text-compact {
            color: white;
        }

        /* 紧凑信息网格样式 */
        .compact-info-grid {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #f0f0f0;
        }

        .compact-row {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
        }

        .compact-row:last-child {
            border-bottom: none;
        }

        .compact-item {
            flex: 1;
            padding: 16px 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            border-right: 1px solid #f0f0f0;
        }

        .compact-item:last-child {
            border-right: none;
        }

        .compact-label {
            font-size: 20px;
            color: #999;
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .compact-value {
            font-size: 24px;
            color: #333;
            font-weight: 500;
            line-height: 1.2;
        }

        .compact-value.highlight {
            color: #667eea;
            font-weight: 600;
        }

        .compact-value.success {
            color: #4CAF50;
            font-weight: 600;
        }

        /* 弹窗底部 - 紧凑版本 */
        .detail-footer {
            padding: 20px 24px 24px;
            display: flex;
            justify-content: center;
            background: white;
        }

        .detail-footer .confirm-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 20px 48px;
            border-radius: 40px;
            font-size: 28px;
            font-weight: 600;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .detail-footer .confirm-button:active {
            transform: translateY(2px);
            box-shadow: 0 3px 12px rgba(102, 126, 234, 0.4);
        }

        .detail-footer .button-text {
            color: white;
        }

        /* 响应式适配 */
        @media screen and (max-width: 768px) {
            .package-detail-modal {
                width: 90vw;
                max-width: 480px;
                max-height: 80vh;
            }

            .modal-title {
                font-size: 28px;
            }

            .package-name-compact {
                font-size: 24px;
            }

            .compact-item {
                padding: 12px 8px;
            }

            .compact-label {
                font-size: 18px;
            }

            .compact-value {
                font-size: 22px;
            }

            .detail-footer .confirm-button {
                padding: 18px 40px;
                font-size: 26px;
            }
        }
    </style>
</head>
<body>
    <button class="demo-button" onclick="showModal()">
        📱 查看紧凑弹窗
    </button>

    <!-- 简洁版套餐详情弹窗 -->
    <div id="packageModal" class="modal-overlay" onclick="closeModal()">
        <div class="package-detail-modal" onclick="event.stopPropagation()">
            <!-- 弹窗头部 -->
            <div class="modal-header">
                <span class="modal-title">套餐激活成功</span>
                <button class="close-btn" onclick="closeModal()">
                    <span class="close-icon">×</span>
                </button>
            </div>

            <!-- 弹窗内容 -->
            <div class="detail-content">
                <!-- 套餐信息 -->
                <div class="package-info-compact">
                    <div class="package-title-compact">
                        <span class="package-name-compact">VIP套餐</span>
                        <div class="active-badge-compact">
                            <span class="badge-text-compact">已激活</span>
                        </div>
                    </div>
                </div>

                <!-- 紧凑数据网格 -->
                <div class="compact-info-grid">
                    <div class="compact-row">
                        <div class="compact-item">
                            <span class="compact-label">开通时间</span>
                            <span class="compact-value">2025-07-30 19:41</span>
                        </div>
                        <div class="compact-item">
                            <span class="compact-label">到期时间</span>
                            <span class="compact-value">2025-08-30 19:41</span>
                        </div>
                    </div>
                    <div class="compact-row">
                        <div class="compact-item">
                            <span class="compact-label">有效期</span>
                            <span class="compact-value highlight">30天</span>
                        </div>
                        <div class="compact-item">
                            <span class="compact-label">获得点数</span>
                            <span class="compact-value success">1000</span>
                        </div>
                    </div>
                    <div class="compact-row">
                        <div class="compact-item">
                            <span class="compact-label">当前余额</span>
                            <span class="compact-value">50.0</span>
                        </div>
                        <div class="compact-item">
                            <span class="compact-label">当前点数</span>
                            <span class="compact-value">1500</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 弹窗底部 -->
            <div class="detail-footer">
                <button class="confirm-button" onclick="closeModal()">
                    <span class="button-text">确定</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showModal() {
            const modal = document.getElementById('packageModal');
            modal.classList.add('show');
        }

        function closeModal() {
            const modal = document.getElementById('packageModal');
            modal.classList.remove('show');
        }

        // 按ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
