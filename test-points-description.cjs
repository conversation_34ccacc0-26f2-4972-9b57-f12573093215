const mysql = require('mysql2/promise');

async function testPointsDescription() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });
    
    console.log('✅ 数据库连接成功\n');
    
    // 测试智能体标题获取逻辑
    const botId = '7491537356286459913'; // 养生计划图文智能体ID
    
    console.log('=== 测试智能体标题获取逻辑 ===');
    console.log(`测试智能体ID: ${botId}\n`);
    
    // 1. 模拟deductUserPoints函数中的标题获取逻辑
    console.log('1. 获取智能体标题:');
    let agentTitle = '智能服务';
    try {
      const [agentThemeResult] = await connection.execute(`
        SELECT title, type FROM agent_theme WHERE agentId = ? OR workflowId = ? LIMIT 1
      `, [botId, botId]);
      
      if (agentThemeResult && agentThemeResult.length > 0) {
        agentTitle = agentThemeResult[0].title || '智能服务';
        console.log(`📋 获取到服务标题: ${agentTitle}`);
        console.log(`📋 服务类型: ${agentThemeResult[0].type}`);
      } else {
        console.log('⚠️ 未找到智能体配置，使用默认标题');
      }
    } catch (titleError) {
      console.log('⚠️ 获取服务标题失败，使用默认值:', titleError.message);
    }
    
    // 2. 模拟点数记录的描述格式
    const pointsToDeduct = 9;
    const description = `${agentTitle}：-${pointsToDeduct}点数`;
    
    console.log('\n2. 点数记录描述格式:');
    console.log(`描述: ${description}`);
    
    // 3. 模拟metadata格式
    const metadata = {
      botId: botId,
      agentTitle: agentTitle,
      pointsDeducted: pointsToDeduct,
      chatId: 'chat_test_123',
      conversationId: 'conv_test_123',
      reason: 'ai_chat_usage',
      deductedAt: new Date().toISOString()
    };
    
    console.log('\n3. metadata格式:');
    console.log(JSON.stringify(metadata, null, 2));
    
    // 4. 查看现有的点数记录格式对比
    console.log('\n=== 现有点数记录对比 ===');
    const [records] = await connection.execute(`
      SELECT id, description, metadata, createdAt 
      FROM points_records 
      WHERE type = 'app_usage' 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    if (records.length > 0) {
      console.log('最近的点数消费记录:');
      records.forEach((record, index) => {
        console.log(`${index + 1}. ID: ${record.id}`);
        console.log(`   描述: ${record.description}`);
        
        // 解析metadata
        try {
          const meta = typeof record.metadata === 'string' 
            ? JSON.parse(record.metadata) 
            : record.metadata;
          if (meta && meta.agentTitle) {
            console.log(`   智能体: ${meta.agentTitle}`);
          }
          if (meta && meta.botId) {
            console.log(`   BotID: ${meta.botId}`);
          }
        } catch (e) {
          console.log(`   metadata解析失败: ${record.metadata}`);
        }
        
        console.log(`   时间: ${record.createdAt}`);
        console.log('   ---');
      });
    } else {
      console.log('❌ 未找到点数消费记录');
    }
    
    // 5. 检查agent_theme表中的所有配置
    console.log('\n=== agent_theme表配置检查 ===');
    const [themes] = await connection.execute(`
      SELECT id, title, type, consumption, agentId, workflowId, enabled
      FROM agent_theme 
      WHERE enabled = 1
      ORDER BY id DESC
    `);
    
    if (themes.length > 0) {
      console.log('启用的智能体和工作流配置:');
      themes.forEach(theme => {
        console.log(`ID: ${theme.id}, 标题: ${theme.title}, 类型: ${theme.type}`);
        console.log(`消耗: ${theme.consumption} 点`);
        console.log(`AgentID: ${theme.agentId || '无'}, WorkflowID: ${theme.workflowId || '无'}`);
        
        // 模拟这个配置的点数记录描述
        const mockDescription = `${theme.title}：-${theme.consumption}点数`;
        console.log(`点数记录描述: ${mockDescription}`);
        console.log('---');
      });
    } else {
      console.log('❌ 未找到启用的配置');
    }
    
    // 6. 验证修复效果
    console.log('\n=== 修复效果验证 ===');
    if (agentTitle !== '智能服务' && agentTitle !== 'AI聊天消费') {
      console.log('🎉 修复成功！点数明细将显示具体的智能体名称');
      console.log(`✅ 新的描述格式: "${description}"`);
      console.log('✅ 用户可以清楚知道使用了哪个智能体');
    } else {
      console.log('❌ 修复可能有问题，仍在使用默认描述');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

testPointsDescription();
