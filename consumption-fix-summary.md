# 工作流和智能体消耗点数修复总结

## 问题描述

之前工作流和智能体的消耗点数存在以下问题：

1. **工作流消耗不一致**：前端传递的消耗点数与数据库配置不一致
2. **智能体消耗硬编码**：智能体消耗点数被硬编码为500，没有从数据库获取
3. **数据格式不统一**：工作流和智能体保存的consumption数据格式不一致
4. **前后端显示不同**：后台管理和前端创作记录显示的消耗点数不一致

## 修复内容

### 1. 工作流消耗点数修复

**文件**: `server/src/routes/coze-proxy.routes.ts`

**修复前**:
```javascript
// 使用前端传递的消耗点数，可能与数据库配置不一致
await deductUserPoints(memberId, consumption, workflow_id, null);

// 保存时使用前端传递的值
consumption: {
  amount: consumption,
  tokens: consumption,
  // ...
}
```

**修复后**:
```javascript
// 从数据库获取实际的消耗点数
const workflowResult = await AppDataSource.query(`
  SELECT consumption FROM agent_theme WHERE workflowId = ? AND enabled = 1 LIMIT 1
`, [workflow_id]);

if (workflowResult && workflowResult.length > 0) {
  actualConsumption = workflowResult[0].consumption;
}

// 使用实际的消耗点数进行扣除
await deductUserPoints(memberId, actualConsumption, workflow_id, null);

// 保存时使用实际的消耗点数
consumption: {
  tokens: actualConsumption,
  inputToken: 0,
  outputToken: 0,
  workflowId: workflow_id,
  workflowTitle: workflowTitle
}
```

### 2. 智能体消耗点数修复

**文件**: `server/src/routes/coze-proxy.routes.ts`

**修复前**:
```javascript
// 硬编码消耗点数
consumption: {
  amount: 500,
  tokens: 500,
  // ...
}
```

**修复后**:
```javascript
// 从数据库获取智能体的实际消耗点数
const agentThemeResult = await AppDataSource.query(`
  SELECT title, icon, consumption, agentId, workflowId
  FROM agent_theme
  WHERE agentId = ? OR workflowId = ?
  ORDER BY id DESC
  LIMIT 1
`, [botId, botId]);

agentConsumption = theme.consumption || 500;

// 使用实际的消耗点数
consumption: {
  tokens: agentConsumption,
  inputToken: userMessage?.content?.length || 0,
  outputToken: assistantReply?.length || 0,
  botId: botId,
  agentName: agentInfo.agentName,
  agentAvatar: agentInfo.agentAvatar
}
```

### 3. 数据格式统一

**修复前**:
- 工作流：简单数字或包含amount字段的对象
- 智能体：包含amount字段的复杂对象

**修复后**:
- 统一格式：都使用包含tokens、inputToken、outputToken的标准JSON对象
- 移除了amount字段，统一使用tokens字段

### 4. JSON序列化修复

**问题**: 原始SQL查询时consumption对象没有被正确序列化

**修复**: 在所有原始SQL INSERT语句中添加`JSON.stringify()`
```javascript
JSON.stringify(taskData.consumption)
```

## 数据库配置示例

当前agent_theme表中的配置：
- logo创作工作流：消耗 10 点
- 养生计划图文智能体：消耗 9 点

## 验证方法

运行测试脚本验证修复效果：
```bash
node test-consumption-fix.cjs
```

测试脚本会检查：
1. agent_theme表中的消耗配置
2. task表中的实际记录
3. 数据一致性验证

## 修复验证

### 测试结果

运行 `node test-agent-consumption-logic.cjs` 的测试结果：

```
=== 测试智能体消耗点数获取逻辑 ===
测试智能体ID: 7491537356286459913

1. 主聊天函数中的消耗点数获取:
✅ 从数据库获取智能体消耗点数: 9

2. saveChatToTaskTable函数中的消耗点数处理:
📋 智能体信息: 养生计划图文, 使用消耗点数: 9

3. 构建consumption对象:
消耗对象: {
  "tokens": 9,
  "inputToken": 10,
  "outputToken": 200,
  "botId": "7491537356286459913",
  "agentName": "养生计划图文",
  "agentAvatar": "/static/images/znt_avatar.png"
}

=== 验证结果 ===
🎉 修复成功！智能体使用了正确的消耗点数 (9点)
```

### 修复的关键问题

1. **智能体调用链修复**：
   - `saveChatHistoryOnly` → `saveChatHistory` → `saveChatToTaskTable`
   - 确保`pointsToDeduct`参数正确传递到最终的保存函数

2. **参数传递修复**：
   ```javascript
   // 修复前：没有传递消耗点数
   await saveChatHistoryOnly(memberId, bot_id, messages, finalResult);

   // 修复后：传递正确的消耗点数
   await saveChatHistoryOnly(memberId, bot_id, messages, finalResult, pointsToDeduct);
   ```

## 预期效果

修复后：
1. **工作流和智能体的消耗点数都从数据库动态获取** ✅
2. **前后端显示的消耗点数保持一致** ✅
3. **task表中保存的consumption数据格式统一** ✅
4. **点数扣除使用正确的配置值** ✅

## 注意事项

1. 现有的历史数据可能仍然显示旧的消耗点数
2. 新的工作流和智能体执行将使用正确的消耗点数
3. 如需修复历史数据，可以运行数据迁移脚本

## 修复状态

- ✅ 工作流消耗点数修复完成
- ✅ 智能体消耗点数修复完成
- ✅ 数据格式统一完成
- ✅ 参数传递链修复完成
- ✅ 测试验证通过
