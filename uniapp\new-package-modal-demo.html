<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新版套餐明细弹窗演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-button {
            background: white;
            color: #667eea;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        /* 套餐明细专用弹窗样式 */
        .package-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.75);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            padding: 20px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .package-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .package-modal-container {
            background: #ffffff;
            border-radius: 16px;
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transform: scale(0.8) translateY(30px);
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .package-modal-overlay.show .package-modal-container {
            transform: scale(1) translateY(0);
        }

        .package-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }

        .header-left {
            flex: 1;
        }

        .modal-title {
            font-size: 18px;
            font-weight: bold;
            color: white;
            display: block;
            margin-bottom: 4px;
        }

        .modal-subtitle {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 300;
        }

        .close-button {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            color: white;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .package-modal-content {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
        }

        .package-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .package-card {
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #f0f0f0;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
        }

        .package-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            border-color: #667eea;
        }

        .package-card-header {
            background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            border-bottom: 1px solid #f0f0f0;
        }

        .package-info {
            flex: 1;
        }

        .package-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .package-meta {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .package-order {
            font-size: 12px;
            color: #666;
        }

        .package-status-badge {
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .status-expired {
            background: #f5f5f5;
            color: #999;
        }

        .package-amount {
            display: flex;
            align-items: baseline;
            color: #667eea;
        }

        .amount-symbol {
            font-size: 12px;
            font-weight: 500;
            margin-right: 2px;
        }

        .amount-value {
            font-size: 20px;
            font-weight: bold;
        }

        .package-card-body {
            padding: 20px;
        }

        .detail-grid {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .detail-section {
            background: #fafbff;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #f0f2ff;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
            display: block;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-row.highlight {
            background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
            border-radius: 6px;
            padding: 10px 12px;
            margin: 4px 0;
            border: 1px solid #ffd700;
        }

        .detail-label {
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }

        .detail-value {
            font-size: 13px;
            color: #333;
            font-weight: 600;
        }

        .detail-value.points {
            color: #667eea;
            font-weight: bold;
        }

        .detail-value.remaining-days {
            color: #ff6b35;
            font-weight: bold;
            font-size: 14px;
        }

        .package-modal-footer {
            background: #f8f9fa;
            padding: 16px 24px;
            text-align: center;
            border-top: 1px solid #f0f0f0;
        }

        .footer-text {
            font-size: 12px;
            color: #999;
        }

        @media (max-width: 768px) {
            .package-modal-container {
                margin: 10px;
                max-height: 95vh;
            }
            
            .package-card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            
            .package-amount {
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <button class="demo-button" onclick="showPackageModal()">
        📦 查看新版套餐明细弹窗
    </button>

    <!-- 套餐明细专用弹窗 -->
    <div id="packageModal" class="package-modal-overlay" onclick="closePackageModal()">
        <div class="package-modal-container" onclick="event.stopPropagation()">
            <!-- 弹窗头部 -->
            <div class="package-modal-header">
                <div class="header-left">
                    <div class="modal-title">📦 我的套餐明细</div>
                    <div class="modal-subtitle">Package Details</div>
                </div>
                <div class="header-right">
                    <button class="close-button" onclick="closePackageModal()">✕</button>
                </div>
            </div>

            <!-- 弹窗内容 -->
            <div class="package-modal-content">
                <!-- 套餐列表 -->
                <div class="package-list">
                    <!-- 使用中的套餐 -->
                    <div class="package-card">
                        <!-- 套餐头部信息 -->
                        <div class="package-card-header">
                            <div class="package-info">
                                <div class="package-title">VIP专业版套餐</div>
                                <div class="package-meta">
                                    <div class="package-order">订单号：ORD20250730001</div>
                                    <div class="package-status-badge status-active">
                                        <div class="status-text">使用中</div>
                                    </div>
                                </div>
                            </div>
                            <div class="package-amount">
                                <div class="amount-symbol">¥</div>
                                <div class="amount-value">99.00</div>
                            </div>
                        </div>

                        <!-- 套餐详细信息 -->
                        <div class="package-card-body">
                            <div class="detail-grid">
                                <!-- 时间信息 -->
                                <div class="detail-section">
                                    <div class="section-title">⏰ 时间信息</div>
                                    <div class="detail-row">
                                        <div class="detail-label">开通时间</div>
                                        <div class="detail-value">2025-07-30 15:38:00</div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">到期时间</div>
                                        <div class="detail-value">2025-08-30 15:38:00</div>
                                    </div>
                                    <div class="detail-row highlight">
                                        <div class="detail-label">剩余天数</div>
                                        <div class="detail-value remaining-days" id="remainingDays">31天</div>
                                    </div>
                                </div>

                                <!-- 配额信息 -->
                                <div class="detail-section">
                                    <div class="section-title">💎 配额信息</div>
                                    <div class="detail-row">
                                        <div class="detail-label">总点数</div>
                                        <div class="detail-value points">10000点</div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">每日限制</div>
                                        <div class="detail-value">500点</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 已过期的套餐 -->
                    <div class="package-card">
                        <div class="package-card-header">
                            <div class="package-info">
                                <div class="package-title">标准版套餐</div>
                                <div class="package-meta">
                                    <div class="package-order">订单号：ORD20241215002</div>
                                    <div class="package-status-badge status-expired">
                                        <div class="status-text">已过期</div>
                                    </div>
                                </div>
                            </div>
                            <div class="package-amount">
                                <div class="amount-symbol">¥</div>
                                <div class="amount-value">59.00</div>
                            </div>
                        </div>

                        <div class="package-card-body">
                            <div class="detail-grid">
                                <div class="detail-section">
                                    <div class="section-title">⏰ 时间信息</div>
                                    <div class="detail-row">
                                        <div class="detail-label">开通时间</div>
                                        <div class="detail-value">2024-12-15 10:20:15</div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">到期时间</div>
                                        <div class="detail-value">2025-01-14 10:20:15</div>
                                    </div>
                                </div>

                                <div class="detail-section">
                                    <div class="section-title">💎 配额信息</div>
                                    <div class="detail-row">
                                        <div class="detail-label">总点数</div>
                                        <div class="detail-value points">5000点</div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">每日限制</div>
                                        <div class="detail-value">不限制</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 弹窗底部 -->
            <div class="package-modal-footer">
                <div class="footer-text">如有疑问，请联系客服</div>
            </div>
        </div>
    </div>

    <script>
        function showPackageModal() {
            document.getElementById('packageModal').classList.add('show');
            calculateRemainingDays();
        }

        function closePackageModal() {
            document.getElementById('packageModal').classList.remove('show');
        }

        function calculateRemainingDays() {
            const expiredTimeStr = "2025-08-30 15:38:00";
            const endDate = new Date(expiredTimeStr);
            const now = new Date();
            const timeDiff = endDate.getTime() - now.getTime();
            const remainingDays = Math.max(0, Math.ceil(timeDiff / (24 * 60 * 60 * 1000)));
            
            document.getElementById('remainingDays').textContent = remainingDays + '天';
        }

        // 页面加载时自动显示弹窗
        window.onload = function() {
            setTimeout(showPackageModal, 500);
        }
    </script>
</body>
</html>
