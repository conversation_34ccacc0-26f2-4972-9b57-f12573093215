<template>
  <div class="customer-service-image" v-if="customerServiceImage">
    <img 
      :src="customerServiceImage" 
      alt="客服联系方式" 
      class="service-image"
      @click="showModal = true"
    />
    
    <!-- 点击放大显示的模态框 -->
    <n-modal v-model:show="showModal" preset="card" style="width: 400px;" title="联系客服">
      <div class="modal-content">
        <img :src="customerServiceImage" alt="客服联系方式" class="modal-image" />
        <p class="service-tip">扫描二维码或保存图片联系客服</p>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { NModal } from 'naive-ui';
import configApi from '@/api/config';

// 响应式数据
const customerServiceImage = ref('');
const showModal = ref(false);

// 加载客服图片配置
async function loadCustomerServiceImage() {
  try {
    const response = await configApi.getCustomerServiceImage();
    if (response && response.data) {
      customerServiceImage.value = response.data.customerServiceImage;
    }
  } catch (error) {
    console.error('加载客服图片失败:', error);
  }
}

// 组件挂载时加载配置
onMounted(() => {
  loadCustomerServiceImage();
});
</script>

<style scoped>
.customer-service-image {
  display: inline-block;
}

.service-image {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  object-fit: cover;
}

.service-image:hover {
  transform: scale(1.1);
}

.modal-content {
  text-align: center;
}

.modal-image {
  width: 100%;
  max-width: 300px;
  height: auto;
  border-radius: 8px;
  margin-bottom: 16px;
}

.service-tip {
  color: #666;
  font-size: 14px;
  margin: 0;
}
</style>
