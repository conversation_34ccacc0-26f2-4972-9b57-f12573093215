<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邀请码功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-link {
            display: block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .code-block {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>邀请码功能测试页面</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>本页面用于测试邀请码功能的各种场景。请按照以下步骤进行测试：</p>
        <ol>
            <li>确保后端服务正在运行</li>
            <li>确保前端UniApp项目正在运行</li>
            <li>准备一个已注册的用户账号作为邀请人</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔗 测试链接</h2>
        <p>点击以下链接测试不同的邀请码场景：</p>
        
        <h3>1. 有效邀请码测试</h3>
        <a href="/#/pages/auth/register?inviteCode=INV123456789&inviter=123" class="test-link" target="_blank">
            测试有效邀请码 (INV123456789)
        </a>
        <div class="code-block">
            邀请码: INV123456789<br>
            邀请人ID: 123<br>
            预期结果: 邀请码自动填入并锁定
        </div>

        <h3>2. 无效邀请码格式测试</h3>
        <a href="/#/pages/auth/register?inviteCode=INVALID123&inviter=123" class="test-link" target="_blank">
            测试无效邀请码格式 (INVALID123)
        </a>
        <div class="code-block">
            邀请码: INVALID123<br>
            预期结果: 注册时显示"邀请码格式不正确"
        </div>

        <h3>3. 正常注册（无邀请码）</h3>
        <a href="/#/pages/auth/register" class="test-link" target="_blank">
            正常注册页面
        </a>
        <div class="code-block">
            预期结果: 邀请码输入框为空且可编辑
        </div>

        <h3>4. 弹窗注册（有邀请码）</h3>
        <a href="/#/?inviteCode=INV123456789" class="test-link" target="_blank">
            弹窗注册（有邀请码）
        </a>
        <div class="code-block">
            预期结果: 自动打开注册弹窗，邀请码自动填入
        </div>

        <h3>5. 弹窗注册（无邀请码）</h3>
        <a href="/#/" class="test-link" target="_blank">
            弹窗注册（无邀请码）
        </a>
        <div class="code-block">
            预期结果: 邀请码输入框为空且可编辑
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 测试用例</h2>
        
        <h3>测试用例1：通过邀请链接注册</h3>
        <ol>
            <li>点击上方"测试有效邀请码"链接</li>
            <li>检查邀请码输入框是否自动填入 <code>INV123456789</code></li>
            <li>检查邀请码输入框是否被锁定（灰色背景，🔒图标）</li>
            <li>检查是否显示提示文字："通过邀请链接注册，邀请码已自动填入"</li>
            <li>填写其他必填字段（联系方式、验证码、密码、确认密码）并完成注册</li>
            <li class="info">预期结果：注册成功，建立上下级关系</li>
        </ol>

        <h3>测试用例2：手动输入邀请码</h3>
        <ol>
            <li>点击"正常注册页面"链接</li>
            <li>检查邀请码输入框是否为空且可编辑</li>
            <li>手动输入有效邀请码（如：INV123456789）</li>
            <li>填写其他必填字段（联系方式、验证码、密码、确认密码）并完成注册</li>
            <li class="info">预期结果：注册成功，建立上下级关系</li>
        </ol>

        <h3>测试用例3：无效邀请码</h3>
        <ol>
            <li>点击"正常注册页面"链接</li>
            <li>手动输入无效邀请码（如：INVALID123）</li>
            <li>填写其他必填字段（联系方式、验证码、密码、确认密码）并尝试注册</li>
            <li class="error">预期结果：显示"邀请码格式不正确"错误</li>
        </ol>

        <h3>测试用例4：空邀请码</h3>
        <ol>
            <li>点击"正常注册页面"链接</li>
            <li>不填写邀请码，直接填写其他必填字段（联系方式、验证码、密码、确认密码）</li>
            <li>完成注册</li>
            <li class="success">预期结果：注册成功，无上下级关系</li>
        </ol>

        <h3>测试用例5：弹窗注册（有邀请码）</h3>
        <ol>
            <li>点击上方"弹窗注册（有邀请码）"链接</li>
            <li>检查是否自动打开注册弹窗</li>
            <li>检查邀请码输入框是否自动填入 <code>INV123456789</code></li>
            <li>填写其他必填字段（联系方式、验证码、密码、确认密码）并完成注册</li>
            <li class="info">预期结果：注册成功，建立上下级关系</li>
        </ol>

        <h3>测试用例6：弹窗注册（无邀请码）</h3>
        <ol>
            <li>点击上方"弹窗注册（无邀请码）"链接</li>
            <li>点击登录按钮打开弹窗，切换到注册模式</li>
            <li>检查邀请码输入框是否为空且可编辑</li>
            <li>填写其他必填字段（联系方式、验证码、密码、确认密码）并完成注册</li>
            <li class="info">预期结果：注册成功，无上下级关系</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔍 验证方法</h2>
        
        <h3>前端验证</h3>
        <ul>
            <li>检查邀请码输入框的显示状态</li>
            <li>检查URL参数解析是否正确</li>
            <li>检查表单验证是否正常工作</li>
            <li>检查注册请求是否包含邀请码</li>
        </ul>

        <h3>后端验证</h3>
        <p>注册成功后，检查数据库中的数据：</p>
        <div class="code-block">
-- 检查新用户的邀请人关系<br>
SELECT id, name, phone, referrerId FROM members WHERE phone = '新用户手机号';<br><br>

-- 检查邀请奖励记录<br>
SELECT * FROM points_records WHERE type = 'invite_reward' ORDER BY createdAt DESC LIMIT 5;<br><br>

-- 检查邀请人积分变化<br>
SELECT id, name, points FROM members WHERE id = 邀请人ID;
        </div>
    </div>

    <div class="test-section">
        <h2>📊 测试结果记录</h2>
        <p>请在测试过程中记录以下信息：</p>
        <ul>
            <li>✅ 邀请链接解析是否正确</li>
            <li>✅ 邀请码自动填入是否正常</li>
            <li>✅ 邀请码锁定状态是否正确</li>
            <li>✅ 表单验证是否正常</li>
            <li>✅ 注册流程是否完整</li>
            <li>✅ 上下级关系是否建立</li>
            <li>✅ 邀请奖励是否发放</li>
            <li>✅ 错误处理是否正确</li>
        </ul>
    </div>

    <script>
        // 简单的测试辅助脚本
        console.log('邀请码功能测试页面已加载');
        
        // 检查当前URL参数
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('inviteCode')) {
            console.log('检测到邀请码参数:', urlParams.get('inviteCode'));
        }
        
        // 生成测试用的邀请码
        function generateTestInviteCode(userId = 123) {
            const timestamp = Date.now().toString().slice(-6);
            return `INV${userId}${timestamp}`;
        }
        
        console.log('测试邀请码示例:', generateTestInviteCode());
    </script>
</body>
</html>
