<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人设置测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .test-btn {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            background: #f0f0f0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h2>个人设置功能测试</h2>
            
            <div class="status" :class="statusClass">
                {{ statusMessage }}
            </div>
            
            <button class="test-btn" @click="testMembersAPI">
                测试 Members API 导入
            </button>
            
            <button class="test-btn" @click="testPersonalModal">
                测试个人设置弹窗
            </button>
            
            <button class="test-btn" @click="testSecurityModal">
                测试账户安全弹窗
            </button>
            
            <button class="test-btn" @click="testAPICall">
                测试 API 调用
            </button>
            
            <div v-if="showResults">
                <h3>测试结果:</h3>
                <pre>{{ testResults }}</pre>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    statusMessage: '准备开始测试...',
                    statusClass: '',
                    showResults: false,
                    testResults: ''
                }
            },
            methods: {
                setStatus(message, type = '') {
                    this.statusMessage = message;
                    this.statusClass = type;
                },
                
                async testMembersAPI() {
                    this.setStatus('测试 Members API 导入...', '');
                    try {
                        // 模拟导入测试
                        const testImport = () => {
                            return new Promise((resolve) => {
                                setTimeout(() => {
                                    resolve({
                                        memberAPI: {
                                            getCurrentUser: () => Promise.resolve({ success: true, data: { id: 1, name: 'Test User' } }),
                                            updateCurrentUser: () => Promise.resolve({ success: true }),
                                            changePassword: () => Promise.resolve({ success: true })
                                        }
                                    });
                                }, 500);
                            });
                        };
                        
                        const result = await testImport();
                        this.setStatus('✅ Members API 导入成功', 'success');
                        this.testResults = JSON.stringify(result, null, 2);
                        this.showResults = true;
                    } catch (error) {
                        this.setStatus('❌ Members API 导入失败: ' + error.message, 'error');
                        this.testResults = error.toString();
                        this.showResults = true;
                    }
                },
                
                async testPersonalModal() {
                    this.setStatus('测试个人设置弹窗组件...', '');
                    try {
                        // 模拟组件测试
                        await new Promise(resolve => setTimeout(resolve, 500));
                        this.setStatus('✅ 个人设置弹窗组件正常', 'success');
                        this.testResults = '个人设置弹窗组件包含:\n- 头像上传\n- 昵称编辑\n- 手机号绑定\n- 邮箱绑定';
                        this.showResults = true;
                    } catch (error) {
                        this.setStatus('❌ 个人设置弹窗组件异常: ' + error.message, 'error');
                    }
                },
                
                async testSecurityModal() {
                    this.setStatus('测试账户安全弹窗组件...', '');
                    try {
                        // 模拟组件测试
                        await new Promise(resolve => setTimeout(resolve, 500));
                        this.setStatus('✅ 账户安全弹窗组件正常', 'success');
                        this.testResults = '账户安全弹窗组件包含:\n- 修改密码\n- 绑定手机号\n- 绑定邮箱\n- 验证码功能';
                        this.showResults = true;
                    } catch (error) {
                        this.setStatus('❌ 账户安全弹窗组件异常: ' + error.message, 'error');
                    }
                },
                
                async testAPICall() {
                    this.setStatus('测试 API 调用...', '');
                    try {
                        // 模拟 API 调用
                        const mockAPI = {
                            getCurrentUser: () => Promise.resolve({
                                success: true,
                                data: {
                                    id: 1,
                                    username: 'testuser',
                                    nickname: '测试用户',
                                    phone: '13800138000',
                                    email: '<EMAIL>',
                                    avatar: '/static/images/default-avatar.png'
                                }
                            })
                        };
                        
                        const result = await mockAPI.getCurrentUser();
                        this.setStatus('✅ API 调用成功', 'success');
                        this.testResults = JSON.stringify(result, null, 2);
                        this.showResults = true;
                    } catch (error) {
                        this.setStatus('❌ API 调用失败: ' + error.message, 'error');
                        this.testResults = error.toString();
                        this.showResults = true;
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
