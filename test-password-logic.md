# 密码逻辑测试说明

## 修改内容总结

### 1. 后端API修改

#### `/api/members/change-password` 端点 (server/src/app.ts)
- **修改前**: 要求所有参数包括currentPassword都必须提供
- **修改后**: 
  - 只要求newPassword和confirmPassword必须提供
  - currentPassword变为可选参数
  - 如果用户已设置密码，则需要验证currentPassword
  - 如果用户未设置密码，则直接设置新密码

#### 获取用户信息API (server/src/controller/MemberController.ts)
- **修改**: 在getCurrentMember方法中添加hasPassword字段
- **目的**: 让前端知道用户是否已设置密码

### 2. 前端修改

#### account-security-modal.vue
- **修改**: hasPassword计算属性从检查`userInfo.password`改为检查`userInfo.hasPassword`
- **原因**: 后端不会返回password字段，而是返回hasPassword布尔值

## 测试场景

### 场景1: 用户未设置密码
1. 用户登录后，hasPassword为false
2. 密码修改界面显示"设置登录密码"
3. 不显示"当前密码"输入框
4. 只需要输入新密码和确认密码
5. 提交时不传递currentPassword参数

### 场景2: 用户已设置密码
1. 用户登录后，hasPassword为true
2. 密码修改界面显示"修改登录密码"
3. 显示"当前密码"输入框
4. 需要输入当前密码、新密码和确认密码
5. 提交时传递currentPassword参数

## 关键代码变更

### 后端API逻辑
```javascript
// 如果用户已设置密码，需要验证当前密码
if (member.password) {
  if (!currentPassword) {
    return res.status(400).json({
      success: false,
      message: '请输入当前密码'
    });
  }
  // 验证当前密码
  const isPasswordValid = await bcrypt.compare(currentPassword, member.password);
  if (!isPasswordValid) {
    return res.status(400).json({
      success: false,
      message: '当前密码不正确'
    });
  }
}
```

### 前端条件渲染
```vue
<!-- 只有已设置密码的用户才需要输入当前密码 -->
<view v-if="hasPassword" class="form-group">
  <!-- 当前密码输入框 -->
</view>
```

### 前端API调用
```javascript
const requestData = {
  newPassword,
  confirmPassword
}

// 只有在用户已设置密码时才传递当前密码
if (this.hasPassword) {
  requestData.currentPassword = currentPassword
}
```

## 预期效果

1. **新用户**: 注册后首次设置密码时，界面简洁，只需要设置新密码
2. **老用户**: 修改密码时需要验证当前密码，确保安全性
3. **用户体验**: 根据用户状态动态调整界面和验证逻辑
4. **安全性**: 已设置密码的用户必须验证当前密码才能修改
