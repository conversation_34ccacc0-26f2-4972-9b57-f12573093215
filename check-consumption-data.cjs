const mysql = require('mysql2/promise');

async function checkConsumptionData() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });
    
    console.log('✅ 数据库连接成功\n');
    
    // 1. 查看task表中的消耗数据
    console.log('=== 查看task表中的消耗数据 ===');
    const [tasks] = await connection.execute(`
      SELECT id, userId, userName, taskType, taskTypeDetail, consumption, status, logs, createTime 
      FROM task 
      ORDER BY id DESC 
      LIMIT 10
    `);
    
    tasks.forEach(task => {
      console.log(`ID: ${task.id}, 用户: ${task.userName}, 类型: ${task.taskType}, 详情: ${task.taskTypeDetail}`);
      console.log(`消耗: ${typeof task.consumption === 'object' ? JSON.stringify(task.consumption) : task.consumption}`);
      console.log(`状态: ${task.status}, 日志: ${task.logs}`);
      console.log(`创建时间: ${task.createTime}`);
      console.log('---');
    });
    
    // 2. 分别查看工作流和智能体的消耗情况
    console.log('\n=== 工作流消耗情况 ===');
    const [workflows] = await connection.execute(`
      SELECT id, userName, taskTypeDetail, consumption, status, createTime 
      FROM task 
      WHERE taskType = '工作流' 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    workflows.forEach(task => {
      console.log(`工作流: ${task.taskTypeDetail}`);
      console.log(`消耗: ${typeof task.consumption === 'object' ? JSON.stringify(task.consumption) : task.consumption}`);
      console.log(`状态: ${task.status}`);
      console.log('---');
    });
    
    console.log('\n=== 智能体消耗情况 ===');
    const [agents] = await connection.execute(`
      SELECT id, userName, taskTypeDetail, consumption, status, createTime 
      FROM task 
      WHERE taskType = '智能体' 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    agents.forEach(task => {
      console.log(`智能体: ${task.taskTypeDetail}`);
      console.log(`消耗: ${typeof task.consumption === 'object' ? JSON.stringify(task.consumption) : task.consumption}`);
      console.log(`状态: ${task.status}`);
      console.log('---');
    });
    
    // 3. 查看agent_theme表结构
    console.log('\n=== agent_theme表结构 ===');
    const [columns] = await connection.execute('DESCRIBE agent_theme');
    columns.forEach(col => {
      console.log(`${col.Field}: ${col.Type}`);
    });

    // 4. 查看agent_theme表中的配置消耗
    console.log('\n=== agent_theme表中的消耗配置 ===');
    const [themes] = await connection.execute(`
      SELECT id, title, type, consumption, workflowId, enabled
      FROM agent_theme
      WHERE enabled = 1
      ORDER BY id DESC
      LIMIT 10
    `);

    themes.forEach(theme => {
      console.log(`ID: ${theme.id}, 标题: ${theme.title}, 类型: ${theme.type}`);
      console.log(`配置消耗: ${theme.consumption}, WorkflowID: ${theme.workflowId}`);
      console.log('---');
    });
    
  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkConsumptionData();
