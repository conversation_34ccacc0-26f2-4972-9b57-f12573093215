# 后端API补充说明

## 需要添加的API端点

为了完整支持个人设置和账户安全功能，需要在后端添加以下API端点：

### 1. 发送验证码 API

```javascript
// POST /api/verification/send
app.post('/api/verification/send', authMiddleware, async (req, res) => {
  try {
    const { contact } = req.body; // 手机号或邮箱
    
    // 验证联系方式格式
    const isPhone = /^1[3-9]\d{9}$/.test(contact);
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact);
    
    if (!isPhone && !isEmail) {
      return res.status(400).json({
        success: false,
        message: '请输入正确的手机号或邮箱'
      });
    }
    
    // 生成6位验证码
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    
    // 存储验证码（实际应该存储到Redis或数据库，这里简化处理）
    // 设置5分钟过期时间
    const expireTime = Date.now() + 5 * 60 * 1000;
    
    // 这里应该调用短信或邮件服务发送验证码
    if (isPhone) {
      console.log(`发送短信验证码到 ${contact}: ${code}`);
      // 调用短信服务API
    } else {
      console.log(`发送邮件验证码到 ${contact}: ${code}`);
      // 调用邮件服务API
    }
    
    // 临时存储验证码（生产环境应使用Redis）
    global.verificationCodes = global.verificationCodes || {};
    global.verificationCodes[contact] = {
      code,
      expireTime,
      attempts: 0
    };
    
    res.json({
      success: true,
      message: '验证码已发送'
    });
    
  } catch (error) {
    console.error('发送验证码失败:', error);
    res.status(500).json({
      success: false,
      message: '发送验证码失败'
    });
  }
});
```

### 2. 绑定手机号 API

```javascript
// POST /api/members/bind-phone
app.post('/api/members/bind-phone', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { phone, code } = req.body;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权的访问'
      });
    }
    
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return res.status(400).json({
        success: false,
        message: '请输入正确的手机号'
      });
    }
    
    // 验证验证码
    const storedData = global.verificationCodes?.[phone];
    if (!storedData) {
      return res.status(400).json({
        success: false,
        message: '验证码已过期，请重新发送'
      });
    }
    
    if (Date.now() > storedData.expireTime) {
      delete global.verificationCodes[phone];
      return res.status(400).json({
        success: false,
        message: '验证码已过期，请重新发送'
      });
    }
    
    if (storedData.code !== code) {
      storedData.attempts++;
      if (storedData.attempts >= 3) {
        delete global.verificationCodes[phone];
        return res.status(400).json({
          success: false,
          message: '验证码错误次数过多，请重新发送'
        });
      }
      return res.status(400).json({
        success: false,
        message: '验证码错误'
      });
    }
    
    // 检查手机号是否已被其他用户绑定
    const [existingUsers] = await pool.execute(
      'SELECT id FROM user WHERE phone = ? AND id != ?',
      [phone, userId]
    );
    
    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该手机号已被其他用户绑定'
      });
    }
    
    // 更新用户手机号
    await pool.execute(
      'UPDATE user SET phone = ?, updated_at = NOW() WHERE id = ?',
      [phone, userId]
    );
    
    // 清除验证码
    delete global.verificationCodes[phone];
    
    res.json({
      success: true,
      message: '手机号绑定成功'
    });
    
  } catch (error) {
    console.error('绑定手机号失败:', error);
    res.status(500).json({
      success: false,
      message: '绑定手机号失败'
    });
  }
});
```

### 3. 绑定邮箱 API

```javascript
// POST /api/members/bind-email
app.post('/api/members/bind-email', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { email, code } = req.body;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权的访问'
      });
    }
    
    // 验证邮箱格式
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.status(400).json({
        success: false,
        message: '请输入正确的邮箱地址'
      });
    }
    
    // 验证验证码（逻辑与手机号相同）
    const storedData = global.verificationCodes?.[email];
    if (!storedData) {
      return res.status(400).json({
        success: false,
        message: '验证码已过期，请重新发送'
      });
    }
    
    if (Date.now() > storedData.expireTime) {
      delete global.verificationCodes[email];
      return res.status(400).json({
        success: false,
        message: '验证码已过期，请重新发送'
      });
    }
    
    if (storedData.code !== code) {
      storedData.attempts++;
      if (storedData.attempts >= 3) {
        delete global.verificationCodes[email];
        return res.status(400).json({
          success: false,
          message: '验证码错误次数过多，请重新发送'
        });
      }
      return res.status(400).json({
        success: false,
        message: '验证码错误'
      });
    }
    
    // 检查邮箱是否已被其他用户绑定
    const [existingUsers] = await pool.execute(
      'SELECT id FROM user WHERE email = ? AND id != ?',
      [email, userId]
    );
    
    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该邮箱已被其他用户绑定'
      });
    }
    
    // 更新用户邮箱
    await pool.execute(
      'UPDATE user SET email = ?, updated_at = NOW() WHERE id = ?',
      [email, userId]
    );
    
    // 清除验证码
    delete global.verificationCodes[email];
    
    res.json({
      success: true,
      message: '邮箱绑定成功'
    });
    
  } catch (error) {
    console.error('绑定邮箱失败:', error);
    res.status(500).json({
      success: false,
      message: '绑定邮箱失败'
    });
  }
});
```

### 4. 更新头像 API

```javascript
// PUT /api/members/avatar
app.put('/api/members/avatar', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { avatar } = req.body;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权的访问'
      });
    }
    
    if (!avatar) {
      return res.status(400).json({
        success: false,
        message: '头像URL不能为空'
      });
    }
    
    // 更新用户头像
    await pool.execute(
      'UPDATE user SET avatar = ?, updated_at = NOW() WHERE id = ?',
      [avatar, userId]
    );
    
    res.json({
      success: true,
      message: '头像更新成功',
      data: { avatar }
    });
    
  } catch (error) {
    console.error('更新头像失败:', error);
    res.status(500).json({
      success: false,
      message: '更新头像失败'
    });
  }
});
```

## 集成到现有后端

### 方式1：添加到 server/app.js

将上述API端点代码添加到现有的 `server/app.js` 文件中，在现有路由之后。

### 方式2：创建独立路由文件

创建 `server/routes/profile-routes.js`：

```javascript
const express = require('express');
const router = express.Router();
const { authMiddleware } = require('../middleware/auth');

// 在这里添加上述所有API端点代码

module.exports = router;
```

然后在 `server/app.js` 中引入：

```javascript
const profileRoutes = require('./routes/profile-routes');
app.use('/api', profileRoutes);
```

## 安全考虑

### 1. 验证码安全
- 限制发送频率（每分钟最多1次）
- 设置过期时间（5分钟）
- 限制尝试次数（最多3次）
- 使用Redis存储验证码（生产环境）

### 2. 数据验证
- 严格的输入格式验证
- 防止SQL注入
- 检查重复绑定

### 3. 权限控制
- 所有API都需要登录验证
- 只能修改自己的信息
- 敏感操作需要二次验证

## 第三方服务集成

### 短信服务
推荐使用：
- 阿里云短信服务
- 腾讯云短信
- 网易云信

### 邮件服务
推荐使用：
- 阿里云邮件推送
- SendGrid
- 腾讯云邮件推送

### 文件上传
推荐使用：
- 阿里云OSS
- 腾讯云COS
- 七牛云存储

## 测试建议

### 单元测试
- API端点功能测试
- 验证码生成和验证逻辑
- 数据库操作测试

### 集成测试
- 完整的绑定流程测试
- 错误处理测试
- 并发访问测试

### 安全测试
- 验证码暴力破解测试
- SQL注入测试
- 权限绕过测试
