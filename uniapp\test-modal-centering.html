<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗居中测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-container {
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            margin: 10px;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .info-text {
            color: white;
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.8;
        }

        /* 弹窗遮罩 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* 测试弹窗 - 完美居中版本 */
        .test-modal {
            width: 680px;
            max-width: 85vw;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 32px 80px rgba(0, 0, 0, 0.12), 0 8px 32px rgba(0, 0, 0, 0.08);
            margin: 0 auto;
            position: relative;
            animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            transform: translateX(0);
        }

        @keyframes modalSlideIn {
            0% {
                opacity: 0;
                transform: scale(0.9) translateY(30px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* 弹窗头部 */
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 20px;
            flex: 1;
        }

        .success-badge {
            width: 64px;
            height: 64px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .success-icon {
            color: white;
            font-size: 32px;
            font-weight: bold;
        }

        .header-text {
            flex: 1;
        }

        .modal-title {
            font-size: 32px;
            font-weight: 600;
            color: white;
            display: block;
            margin-bottom: 4px;
        }

        .modal-subtitle {
            font-size: 24px;
            color: rgba(255, 255, 255, 0.8);
            display: block;
        }

        .close-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: none;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.1);
        }

        .close-icon {
            font-size: 32px;
            color: white;
            font-weight: 300;
        }

        /* 弹窗内容 */
        .modal-content {
            padding: 32px;
            text-align: center;
        }

        .content-text {
            font-size: 18px;
            color: #333;
            line-height: 1.6;
            margin-bottom: 24px;
        }

        .centering-info {
            background: #f8f9ff;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .centering-info h3 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .centering-info p {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 弹窗底部 */
        .modal-footer {
            padding: 24px 32px 32px;
            display: flex;
            justify-content: center;
            background: #fafbfc;
            border-top: 1px solid #f0f0f0;
        }

        .confirm-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 48px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .confirm-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        /* 居中指示线 */
        .center-guide {
            position: fixed;
            top: 0;
            left: 50%;
            width: 2px;
            height: 100vh;
            background: rgba(255, 0, 0, 0.3);
            z-index: 999;
            transform: translateX(-1px);
            pointer-events: none;
        }

        .center-guide.show {
            display: block;
        }

        /* 响应式适配 */
        @media screen and (max-width: 768px) {
            .test-modal {
                width: 90vw;
                max-width: 640px;
                max-height: 90vh;
                margin: 0 auto;
                transform: translateX(0);
            }

            .modal-header {
                padding: 24px;
            }

            .header-content {
                gap: 16px;
            }

            .success-badge {
                width: 56px;
                height: 56px;
            }

            .success-icon {
                font-size: 28px;
            }

            .modal-title {
                font-size: 28px;
            }

            .modal-subtitle {
                font-size: 22px;
            }

            .close-btn {
                width: 48px;
                height: 48px;
            }

            .close-icon {
                font-size: 28px;
            }

            .modal-content {
                padding: 24px;
            }

            .modal-footer {
                padding: 20px 24px 24px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <button class="demo-button" onclick="showModal()">
            🎯 测试弹窗居中效果
        </button>
        <button class="demo-button" onclick="toggleCenterGuide()">
            📏 显示/隐藏中心线
        </button>
        <div class="info-text">
            点击按钮测试弹窗是否完美居中<br>
            红色中心线可以帮助验证居中效果
        </div>
    </div>

    <!-- 中心指示线 -->
    <div id="centerGuide" class="center-guide" style="display: none;"></div>

    <!-- 测试弹窗 -->
    <div id="testModal" class="modal-overlay" onclick="closeModal()">
        <div class="test-modal" onclick="event.stopPropagation()">
            <!-- 弹窗头部 -->
            <div class="modal-header">
                <div class="header-content">
                    <div class="success-badge">
                        <span class="success-icon">🎯</span>
                    </div>
                    <div class="header-text">
                        <span class="modal-title">居中测试</span>
                        <span class="modal-subtitle">Centering Test</span>
                    </div>
                </div>
                <button class="close-btn" onclick="closeModal()">
                    <span class="close-icon">×</span>
                </button>
            </div>

            <!-- 弹窗内容 -->
            <div class="modal-content">
                <p class="content-text">
                    这个弹窗应该在屏幕中完美居中，左右两边的间距应该完全相等。
                </p>
                
                <div class="centering-info">
                    <h3>🔧 修复要点</h3>
                    <p>
                        • 弹窗宽度：680px (85vw max)<br>
                        • 使用 margin: 0 auto 确保水平居中<br>
                        • 遮罩层使用 flexbox 居中<br>
                        • 添加 transform: translateX(0) 重置偏移
                    </p>
                </div>

                <p class="content-text">
                    请观察弹窗是否在红色中心线两边对称分布。
                </p>
            </div>

            <!-- 弹窗底部 -->
            <div class="modal-footer">
                <button class="confirm-button" onclick="closeModal()">
                    确认居中效果
                </button>
            </div>
        </div>
    </div>

    <script>
        function showModal() {
            const modal = document.getElementById('testModal');
            modal.classList.add('show');
        }

        function closeModal() {
            const modal = document.getElementById('testModal');
            modal.classList.remove('show');
        }

        function toggleCenterGuide() {
            const guide = document.getElementById('centerGuide');
            if (guide.style.display === 'none') {
                guide.style.display = 'block';
            } else {
                guide.style.display = 'none';
            }
        }

        // 按ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
