#!/usr/bin/env node

/**
 * 快速切换控制台日志开关
 * 使用方法：
 * node toggle-logs.js on   # 开启日志
 * node toggle-logs.js off  # 关闭日志
 * node toggle-logs.js      # 查看当前状态并切换
 */

const fs = require('fs');
const path = require('path');

const configPath = path.join(__dirname, 'config', 'index.js');

function readConfig() {
    try {
        const content = fs.readFileSync(configPath, 'utf8');
        const match = content.match(/export const enableConsoleLog = (true|false)/);
        return match ? match[1] === 'true' : null;
    } catch (error) {
        console.error('❌ 读取配置文件失败:', error.message);
        return null;
    }
}

function writeConfig(enable) {
    try {
        let content = fs.readFileSync(configPath, 'utf8');
        content = content.replace(
            /export const enableConsoleLog = (true|false)/,
            `export const enableConsoleLog = ${enable}`
        );
        fs.writeFileSync(configPath, content, 'utf8');
        return true;
    } catch (error) {
        console.error('❌ 写入配置文件失败:', error.message);
        return false;
    }
}

function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    console.log('🔧 控制台日志开关工具\n');
    
    const currentState = readConfig();
    if (currentState === null) {
        console.error('❌ 无法读取当前配置');
        process.exit(1);
    }
    
    console.log(`📊 当前状态: ${currentState ? '🔧 已启用' : '🔇 已禁用'}`);
    
    let newState;
    
    switch (command) {
        case 'on':
        case 'enable':
        case 'true':
            newState = true;
            break;
            
        case 'off':
        case 'disable':
        case 'false':
            newState = false;
            break;
            
        case 'toggle':
        case undefined:
            newState = !currentState;
            break;
            
        case 'status':
        case 'check':
            console.log('\n✅ 当前配置检查完成');
            console.log('💡 使用说明:');
            console.log('  node toggle-logs.js on   # 开启日志');
            console.log('  node toggle-logs.js off  # 关闭日志');
            console.log('  node toggle-logs.js      # 切换状态');
            return;
            
        default:
            console.log('❌ 未知命令:', command);
            console.log('💡 使用说明:');
            console.log('  node toggle-logs.js on   # 开启日志');
            console.log('  node toggle-logs.js off  # 关闭日志');
            console.log('  node toggle-logs.js      # 切换状态');
            process.exit(1);
    }
    
    if (newState === currentState) {
        console.log(`\n⚠️  日志已经是${newState ? '启用' : '禁用'}状态，无需更改`);
        return;
    }
    
    console.log(`\n🔄 正在${newState ? '启用' : '禁用'}控制台日志...`);
    
    if (writeConfig(newState)) {
        console.log(`✅ 成功${newState ? '启用' : '禁用'}控制台日志`);
        console.log(`📊 新状态: ${newState ? '🔧 已启用' : '🔇 已禁用'}`);
        console.log('\n💡 提示:');
        console.log('  - 需要刷新页面让配置生效');
        console.log('  - 也可以点击页面右下角的按钮实时切换');
        
        if (newState) {
            console.log('\n🎯 开发测试模式:');
            console.log('  - 所有控制台日志将显示');
            console.log('  - 方便调试和开发');
        } else {
            console.log('\n🎯 生产模式:');
            console.log('  - 控制台日志已隐藏');
            console.log('  - 仅显示错误信息');
            console.log('  - 页面运行更清爽');
        }
    } else {
        console.log('❌ 配置更新失败');
        process.exit(1);
    }
}

main();
