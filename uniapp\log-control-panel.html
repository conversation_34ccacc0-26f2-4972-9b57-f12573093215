<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 日志控制面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .panel {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #007AFF;
        }
        
        .status-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .status-enabled {
            color: #28a745;
        }
        
        .status-disabled {
            color: #dc3545;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 30px 0;
        }
        
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .btn:hover {
            background: #0056CC;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,122,255,0.3);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .instructions {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .instructions h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .instructions ul {
            margin-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
            color: #333;
        }
        
        .code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .toast.show {
            transform: translateX(0);
        }
        
        .toast.success {
            background: #28a745;
        }
        
        .toast.error {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="panel">
        <h1 class="title">🔧 日志控制面板</h1>
        <p class="subtitle">开发测试时的日志管理工具</p>
        
        <div class="status-card">
            <div class="status-title">当前日志状态</div>
            <div id="status" class="status-value">检测中...</div>
            <div id="statusDesc" style="color: #666; margin-top: 10px;"></div>
        </div>
        
        <div class="buttons">
            <button class="btn btn-success" onclick="enableLogs()">
                🔧 启用日志
            </button>
            <button class="btn btn-danger" onclick="disableLogs()">
                🔇 禁用日志
            </button>
            <button class="btn btn-secondary" onclick="testLogs()">
                🧪 测试日志
            </button>
            <button class="btn" onclick="refreshPage()">
                🔄 刷新页面
            </button>
        </div>
        
        <div class="instructions">
            <h4>📋 使用说明</h4>
            <ul>
                <li><strong>启用日志</strong>：显示所有控制台日志，方便开发调试</li>
                <li><strong>禁用日志</strong>：隐藏日志输出，保持控制台清爽</li>
                <li><strong>测试日志</strong>：输出各种类型的日志来验证效果</li>
                <li><strong>实时切换</strong>：在页面右下角有快捷按钮</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h4>⚙️ 永久配置</h4>
            <ul>
                <li>修改 <span class="code">config/index.js</span> 中的 <span class="code">enableConsoleLog</span></li>
                <li>开发测试：<span class="code">enableConsoleLog = true</span></li>
                <li>开发完成：<span class="code">enableConsoleLog = false</span></li>
                <li>命令行工具：<span class="code">node toggle-logs.js on/off</span></li>
            </ul>
        </div>
    </div>
    
    <div id="toast" class="toast"></div>

    <script>
        // 检查是否有控制台管理器
        function checkConsoleManager() {
            return typeof window !== 'undefined' && window.consoleManager;
        }
        
        // 显示Toast
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = `toast ${type} show`;
            
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }
        
        // 更新状态显示
        function updateStatus() {
            const statusEl = document.getElementById('status');
            const statusDescEl = document.getElementById('statusDesc');
            
            if (!checkConsoleManager()) {
                statusEl.textContent = '❌ 未检测到控制台管理器';
                statusEl.className = 'status-value status-disabled';
                statusDescEl.textContent = '请在主应用页面中使用此功能';
                return;
            }
            
            // 简单测试：尝试输出一个日志看是否被禁用
            const originalLog = console.log;
            let isEnabled = true;
            
            console.log = function() {
                isEnabled = false;
            };
            
            // 测试
            console.log('test');
            
            // 恢复
            console.log = originalLog;
            
            if (isEnabled) {
                statusEl.textContent = '🔧 已启用';
                statusEl.className = 'status-value status-enabled';
                statusDescEl.textContent = '所有控制台日志正在显示';
            } else {
                statusEl.textContent = '🔇 已禁用';
                statusEl.className = 'status-value status-disabled';
                statusDescEl.textContent = '控制台日志已隐藏（仅显示错误）';
            }
        }
        
        // 启用日志
        function enableLogs() {
            if (!checkConsoleManager()) {
                showToast('❌ 控制台管理器未找到', 'error');
                return;
            }
            
            window.consoleManager.enable();
            showToast('🔧 日志已启用', 'success');
            updateStatus();
            console.log('✅ 控制台日志已启用');
        }
        
        // 禁用日志
        function disableLogs() {
            if (!checkConsoleManager()) {
                showToast('❌ 控制台管理器未找到', 'error');
                return;
            }
            
            window.consoleManager.disable();
            showToast('🔇 日志已禁用', 'success');
            updateStatus();
            // 使用原始console显示消息
            window.consoleManager.originalConsole.log('🔇 控制台日志已禁用');
        }
        
        // 测试日志
        function testLogs() {
            console.log('📝 这是一条普通日志');
            console.warn('⚠️ 这是一条警告日志');
            console.info('ℹ️ 这是一条信息日志');
            console.debug('🐛 这是一条调试日志');
            console.error('❌ 这是一条错误日志（始终显示）');
            console.trace('🔍 这是一条追踪日志');
            
            showToast('🧪 测试日志已输出，请查看控制台', 'success');
        }
        
        // 刷新页面
        function refreshPage() {
            window.location.reload();
        }
        
        // 页面加载时更新状态
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus();
            
            // 每5秒更新一次状态
            setInterval(updateStatus, 5000);
        });
    </script>
</body>
</html>
