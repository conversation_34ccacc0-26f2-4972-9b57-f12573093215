<template>
	<view v-if="visible" class="modal-overlay" @click="closeModal">
		<view class="modal-card" @click.stop>
			<view class="modal-header">
				<text class="modal-title">账户安全</text>
				<view class="modal-close" @click="closeModal">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<view class="modal-content">
				<!-- 修改密码 -->
				<view class="security-section">
					<view class="section-title">
						<text class="title-icon">🔐</text>
						<text class="title-text">{{ hasPassword ? '修改登录密码' : '设置登录密码' }}</text>
					</view>
					<view class="section-desc">{{ hasPassword ? '定期修改密码，保护账户安全' : '设置登录密码，保护账户安全' }}</view>

					<!-- 只有已设置密码的用户才需要输入当前密码 -->
					<view v-if="hasPassword" class="form-group">
						<view class="input-label">
							<text class="label-text">当前密码</text>
							<text class="label-icon">🔒</text>
						</view>
						<view class="input-wrapper">
							<textarea
								v-model="passwordForm.currentPassword"
								class="security-input password-input"
								placeholder="请输入当前密码"
								:password="true"
								:auto-height="false"
								:show-confirm-bar="false"
								@input="handlePasswordInput('currentPassword', $event)"
							></textarea>
						</view>
					</view>

					<view class="form-group">
						<view class="input-label">
							<text class="label-text">新密码</text>
							<text class="label-icon">🔑</text>
						</view>
						<view class="input-wrapper">
							<textarea
								v-model="passwordForm.newPassword"
								class="security-input password-input"
								placeholder="请输入新密码（6-20位）"
								:password="true"
								:auto-height="false"
								:show-confirm-bar="false"
								maxlength="20"
								@input="handlePasswordInput('newPassword', $event)"
							></textarea>
						</view>
					</view>

					<view class="form-group">
						<view class="input-label">
							<text class="label-text">确认新密码</text>
							<text class="label-icon">✅</text>
						</view>
						<view class="input-wrapper">
							<textarea
								v-model="passwordForm.confirmPassword"
								class="security-input password-input"
								placeholder="请再次输入新密码"
								:password="true"
								:auto-height="false"
								:show-confirm-bar="false"
								maxlength="20"
								@input="handlePasswordInput('confirmPassword', $event)"
							></textarea>
						</view>
					</view>

					<!-- 密码修改提示信息 -->
					<view v-if="passwordMessage" class="message-box" :class="passwordMessageType">
						<text class="message-icon">{{ passwordMessageType === 'success' ? '✅' : '❌' }}</text>
						<text class="message-text">{{ passwordMessage }}</text>
					</view>

					<button class="action-btn password-btn" @click="changePassword" :disabled="changingPassword">
						<text class="btn-icon">🔐</text>
						<text>{{ changingPassword ? '修改中...' : (hasPassword ? '修改密码' : '设置密码') }}</text>
					</button>
				</view>

				<!-- 绑定联系方式 -->
				<view class="security-section">
					<view class="section-title">
						<text class="title-icon">{{ contactIcon }}</text>
						<text class="title-text">绑定联系方式</text>
						<text v-if="hasContact" class="status-badge bound">已绑定</text>
						<text v-else class="status-badge unbound">未绑定</text>
					</view>
					<view class="section-desc">{{ contactDescription }}</view>

					<!-- 已绑定状态显示 -->
					<view v-if="hasContact" class="bound-info">
						<view class="bound-item">
							<text class="bound-icon">{{ userInfo.phone ? '📱' : '📧' }}</text>
							<text class="bound-text">{{ userInfo.phone || userInfo.email }}</text>
							<text class="bound-label">{{ userInfo.phone ? '手机号' : '邮箱' }}</text>
						</view>
						<button class="change-btn" @click="showChangeContact">
							<text class="btn-icon">🔄</text>
							<text>更换联系方式</text>
						</button>
					</view>

					<!-- 绑定/更换表单 -->
					<view v-if="!hasContact || showingChangeForm" class="contact-form">
						<view class="form-group">
							<view class="input-label">
								<text class="label-text">{{ contactInputLabel }}</text>
								<text class="label-icon">{{ contactInputIcon }}</text>
							</view>
							<view class="input-wrapper">
								<textarea
									v-model="contactForm.contact"
									class="security-input contact-input"
									:placeholder="contactInputPlaceholder"
									:auto-height="false"
									:show-confirm-bar="false"
									@input="handleContactInput"
									@blur="detectContactType"
								></textarea>
							</view>
							<view v-if="contactType" class="contact-type-hint">
								<text class="hint-icon">{{ contactType === 'phone' ? '📱' : '📧' }}</text>
								<text class="hint-text">识别为{{ contactType === 'phone' ? '手机号' : '邮箱地址' }}</text>
							</view>
						</view>

						<view class="form-group">
							<view class="input-label">
								<text class="label-text">验证码</text>
								<text class="label-icon">🔢</text>
							</view>
							<view class="verification-group">
								<textarea
									v-model="contactForm.code"
									class="security-input verification-input code-input"
									placeholder="请输入验证码"
									:auto-height="false"
									:show-confirm-bar="false"
									maxlength="6"
									@input="handleCodeInput"
								></textarea>
								<button
									class="send-code-btn"
									@click="sendContactCode"
									:disabled="!contactForm.contact || !contactType || codeSending || countdown > 0"
								>
									<text class="btn-icon">📤</text>
									<text>{{ countdown > 0 ? `${countdown}s` : '发送验证码' }}</text>
								</button>
							</view>
						</view>

						<button class="action-btn contact-btn" @click="bindContact" :disabled="bindingContact">
							<text class="btn-icon">🔗</text>
							<text>{{ bindingContact ? '绑定中...' : (hasContact ? '更换联系方式' : '绑定联系方式') }}</text>
						</button>

						<button v-if="showingChangeForm" class="cancel-btn" @click="cancelChange">
							<text>取消更换</text>
						</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import memberAPI from '@/api/members.js'

export default {
	name: 'AccountSecurityModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		userInfo: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			// 密码修改表单
			passwordForm: {
				currentPassword: '',
				newPassword: '',
				confirmPassword: ''
			},
			changingPassword: false,
			passwordMessage: '', // 密码修改提示信息
			passwordMessageType: 'success', // 'success' 或 'error'

			// 联系方式绑定表单
			contactForm: {
				contact: '',
				code: ''
			},
			contactType: '', // 'phone' 或 'email'
			bindingContact: false,
			codeSending: false,
			countdown: 0,
			timer: null,
			showingChangeForm: false
		}
	},
	computed: {
		// 是否已有联系方式
		hasContact() {
			return !!(this.userInfo.phone || this.userInfo.email)
		},

		// 是否已设置密码
		hasPassword() {
			return !!(this.userInfo.hasPassword)
		},

		// 联系方式图标
		contactIcon() {
			if (this.userInfo.phone && this.userInfo.email) return '📱📧'
			if (this.userInfo.phone) return '📱'
			if (this.userInfo.email) return '📧'
			return '📞'
		},

		// 联系方式描述
		contactDescription() {
			if (this.hasContact) {
				return '用于账户找回和安全验证，建议绑定手机号或邮箱其中一项'
			}
			return '绑定手机号或邮箱用于账户找回和安全验证'
		},

		// 联系方式输入标签
		contactInputLabel() {
			if (this.contactType === 'phone') return '手机号'
			if (this.contactType === 'email') return '邮箱地址'
			return '手机号或邮箱'
		},

		// 联系方式输入图标
		contactInputIcon() {
			if (this.contactType === 'phone') return '📱'
			if (this.contactType === 'email') return '📧'
			return '📞'
		},

		// 联系方式输入占位符
		contactInputPlaceholder() {
			if (this.contactType === 'phone') return '请输入手机号'
			if (this.contactType === 'email') return '请输入邮箱地址'
			return '请输入手机号或邮箱地址'
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.initFormData()
			} else {
				this.resetForms()
			}
		}
	},
	beforeDestroy() {
		this.clearTimer()
	},
	methods: {
		// 初始化表单数据
		initFormData() {
			console.log('🔧 初始化账户安全表单数据')
			this.contactForm.contact = this.userInfo.phone || this.userInfo.email || ''
			this.detectContactType()
			this.showingChangeForm = false
		},

		// 重置表单
		resetForms() {
			this.passwordForm = {
				currentPassword: '',
				newPassword: '',
				confirmPassword: ''
			}
			this.contactForm = {
				contact: this.userInfo.phone || this.userInfo.email || '',
				code: ''
			}
			this.contactType = ''
			this.showingChangeForm = false
			this.passwordMessage = '' // 清除密码提示信息
			this.clearTimer()
		},

		// 清除定时器
		clearTimer() {
			if (this.timer) {
				clearInterval(this.timer)
				this.timer = null
				this.countdown = 0
			}
		},

		// 关闭弹窗
		closeModal() {
			this.$emit('close')
		},

		// 处理密码输入
		handlePasswordInput(field, e) {
			console.log('🔐 密码输入:', field, e)
			const value = e.detail ? e.detail.value : (e.target ? e.target.value : '')
			this.passwordForm[field] = value
			console.log('🔐 密码表单:', this.passwordForm)
		},

		// 处理联系方式输入
		handleContactInput(e) {
			console.log('📞 联系方式输入:', e)
			const value = e.detail ? e.detail.value : (e.target ? e.target.value : '')
			this.contactForm.contact = value
			this.detectContactType()
			console.log('📞 联系方式表单:', this.contactForm)
		},

		// 处理验证码输入
		handleCodeInput(e) {
			console.log('🔢 验证码输入:', e)
			const value = e.detail ? e.detail.value : (e.target ? e.target.value : '')
			this.contactForm.code = value
			console.log('🔢 验证码:', this.contactForm.code)
		},

		// 检测联系方式类型
		detectContactType() {
			const contact = this.contactForm.contact.trim()
			if (!contact) {
				this.contactType = ''
				return
			}

			// 检测是否为手机号
			if (/^1[3-9]\d{9}$/.test(contact)) {
				this.contactType = 'phone'
			}
			// 检测是否为邮箱
			else if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact)) {
				this.contactType = 'email'
			}
			// 其他情况
			else {
				this.contactType = ''
			}

			console.log('🔍 检测联系方式类型:', contact, '->', this.contactType)
		},

		// 显示更换联系方式表单
		showChangeContact() {
			this.showingChangeForm = true
			this.contactForm.contact = ''
			this.contactForm.code = ''
			this.contactType = ''
		},

		// 取消更换
		cancelChange() {
			this.showingChangeForm = false
			this.contactForm.contact = this.userInfo.phone || this.userInfo.email || ''
			this.contactForm.code = ''
			this.detectContactType()
		},

		// 修改密码
		async changePassword() {
			try {
				console.log('🔐 开始修改密码')

				// 清除之前的提示信息
				this.passwordMessage = ''

				const { currentPassword, newPassword, confirmPassword } = this.passwordForm

				// 验证表单
				// 如果用户已设置密码，需要验证当前密码；如果未设置密码，则不需要当前密码
				if (this.hasPassword && !currentPassword) {
					this.showPasswordMessage('请输入当前密码', 'error')
					return
				}

				if (!newPassword || !confirmPassword) {
					this.showPasswordMessage('请填写新密码和确认密码', 'error')
					return
				}

				if (newPassword.length < 6) {
					this.showPasswordMessage('新密码至少6位', 'error')
					return
				}

				if (newPassword !== confirmPassword) {
					this.showPasswordMessage('两次密码输入不一致', 'error')
					return
				}

				this.changingPassword = true

				// 构建请求参数，如果用户没有设置密码则不传递currentPassword
				const requestData = {
					newPassword,
					confirmPassword
				}

				// 只有在用户已设置密码时才传递当前密码
				if (this.hasPassword) {
					requestData.currentPassword = currentPassword
				}

				console.log('发送密码修改请求:', requestData)
				const response = await memberAPI.changePassword(requestData)
				console.log('密码修改响应:', response)

				// 检查响应格式
				if (response && response.success) {
					const successMessage = response.message || (this.hasPassword ? '密码修改成功' : '密码设置成功')
					this.showPasswordMessage(successMessage, 'success')

					// 重置密码表单
					this.passwordForm = {
						currentPassword: '',
						newPassword: '',
						confirmPassword: ''
					}
				} else {
					// 处理失败响应
					const errorMessage = response?.message || '修改密码失败'
					this.showPasswordMessage(errorMessage, 'error')
				}

			} catch (error) {
				console.error('修改密码失败:', error)
				console.log('错误对象详情:', {
					message: error.message,
					data: error.data,
					statusCode: error.statusCode,
					type: typeof error
				})

				// 处理不同类型的错误，优先显示后端返回的具体错误信息
				let errorMessage = '修改密码失败'

				if (error && typeof error === 'object') {
					// 现在members.js的request函数已经正确提取了后端错误信息到error.message
					if (error.message) {
						errorMessage = error.message
					}
					// 备选：从error.data中获取
					else if (error.data && error.data.message) {
						errorMessage = error.data.message
					}
				} else if (typeof error === 'string') {
					errorMessage = error
				}

				this.showPasswordMessage(errorMessage, 'error')
			} finally {
				this.changingPassword = false
			}
		},

		// 显示密码修改提示信息
		showPasswordMessage(message, type = 'error') {
			this.passwordMessage = message
			this.passwordMessageType = type

			// 如果是成功信息，3秒后自动清除
			if (type === 'success') {
				setTimeout(() => {
					this.passwordMessage = ''
				}, 3000)
			}
		},

		// 发送联系方式验证码
		async sendContactCode() {
			try {
				console.log('📤 发送验证码:', this.contactForm.contact, this.contactType)

				if (!this.contactForm.contact || !this.contactType) {
					uni.showToast({
						title: '请输入有效的手机号或邮箱',
						icon: 'none'
					})
					return
				}

				// 验证格式
				if (this.contactType === 'phone' && !/^1[3-9]\d{9}$/.test(this.contactForm.contact)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}

				if (this.contactType === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.contactForm.contact)) {
					uni.showToast({
						title: '请输入正确的邮箱',
						icon: 'none'
					})
					return
				}

				this.codeSending = true

				await memberAPI.sendVerificationCode(this.contactForm.contact)

				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				})

				// 开始倒计时
				this.startCountdown()

			} catch (error) {
				console.error('发送验证码失败:', error)
				uni.showToast({
					title: error.message || '发送失败',
					icon: 'none'
				})
			} finally {
				this.codeSending = false
			}
		},

		// 开始验证码倒计时
		startCountdown() {
			this.countdown = 60
			this.timer = setInterval(() => {
				this.countdown--
				if (this.countdown <= 0) {
					clearInterval(this.timer)
					this.timer = null
				}
			}, 1000)
		},

		// 绑定联系方式
		async bindContact() {
			try {
				console.log('🔗 绑定联系方式:', this.contactForm, this.contactType)

				const { contact, code } = this.contactForm

				if (!contact || !code || !this.contactType) {
					uni.showToast({
						title: '请填写完整信息',
						icon: 'none'
					})
					return
				}

				this.bindingContact = true

				// 根据类型调用不同的API
				if (this.contactType === 'phone') {
					await memberAPI.bindPhone({ phone: contact, code })
					uni.showToast({
						title: '手机号绑定成功',
						icon: 'success'
					})
					this.$emit('security-updated', { phone: contact })
				} else if (this.contactType === 'email') {
					await memberAPI.bindEmail({ email: contact, code })
					uni.showToast({
						title: '邮箱绑定成功',
						icon: 'success'
					})
					this.$emit('security-updated', { email: contact })
				}

				// 重置表单
				this.contactForm.code = ''
				this.showingChangeForm = false

			} catch (error) {
				console.error('绑定联系方式失败:', error)
				uni.showToast({
					title: error.message || '绑定失败',
					icon: 'none'
				})
			} finally {
				this.bindingContact = false
			}
		}
	}
}
</script>

<style scoped>
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	backdrop-filter: blur(10px);
}

.modal-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20px;
	width: 400px;
	max-width: 90%;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: scale(0.8) translateY(20px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
	color: white;
	font-size: 18px;
	font-weight: bold;
}

.modal-close {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.close-icon {
	color: white;
	font-size: 16px;
}

.modal-content {
	padding: 20px;
	max-height: 500px;
	overflow-y: auto;
}

.security-section {
	margin-bottom: 30px;
	padding-bottom: 20px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.security-section:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}

.title-icon {
	font-size: 18px;
	margin-right: 8px;
}

.title-text {
	color: white;
	font-size: 16px;
	font-weight: bold;
	flex: 1;
}

.status-badge {
	padding: 2px 8px;
	border-radius: 10px;
	font-size: 12px;
	font-weight: bold;
}

.status-badge.bound {
	background: rgba(76, 175, 80, 0.3);
	color: #4caf50;
}

.status-badge.unbound {
	background: rgba(255, 152, 0, 0.3);
	color: #ff9800;
}

.section-desc {
	color: rgba(255, 255, 255, 0.7);
	font-size: 12px;
	margin-bottom: 15px;
}

.form-group {
	margin-bottom: 15px;
}

.input-label {
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: rgba(255, 255, 255, 0.9);
	font-size: 14px;
	margin-bottom: 8px;
}

.label-text {
	font-weight: 500;
}

.label-icon {
	font-size: 16px;
	opacity: 0.8;
}

.input-wrapper {
	position: relative;
}

.security-input {
	width: 100%;
	padding: 15px 18px;
	border: 2px solid rgba(255, 255, 255, 0.2);
	border-radius: 12px;
	background: rgba(255, 255, 255, 0.1);
	color: white;
	font-size: 15px;
	box-sizing: border-box;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
	font-family: inherit;
}

.security-input:focus {
	border-color: rgba(255, 255, 255, 0.4);
	background: rgba(255, 255, 255, 0.15);
	box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.security-input::placeholder {
	color: rgba(255, 255, 255, 0.5);
}

.password-input {
	min-height: 50px;
	max-height: 50px;
	resize: none;
	line-height: 1.4;
}

.contact-input {
	min-height: 50px;
	max-height: 50px;
	resize: none;
	line-height: 1.4;
}

.code-input {
	min-height: 48px;
	max-height: 48px;
	resize: none;
	line-height: 48px;
	text-align: center;
	font-size: 14px;
	font-weight: 500;
	letter-spacing: 1px;
	padding: 0 15px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.contact-type-hint {
	display: flex;
	align-items: center;
	margin-top: 8px;
	padding: 8px 12px;
	background: rgba(76, 175, 80, 0.2);
	border-radius: 8px;
	border-left: 3px solid #4caf50;
}

.hint-icon {
	margin-right: 6px;
	font-size: 14px;
}

.hint-text {
	color: #4caf50;
	font-size: 12px;
	font-weight: 500;
}

.bound-info {
	padding: 20px;
	background: rgba(76, 175, 80, 0.1);
	border-radius: 12px;
	border: 1px solid rgba(76, 175, 80, 0.3);
	margin-bottom: 15px;
}

.bound-item {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}

.bound-icon {
	font-size: 20px;
	margin-right: 12px;
}

.bound-text {
	color: white;
	font-size: 16px;
	font-weight: 500;
	flex: 1;
}

.bound-label {
	color: #4caf50;
	font-size: 12px;
	background: rgba(76, 175, 80, 0.2);
	padding: 4px 8px;
	border-radius: 6px;
}

.change-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	padding: 10px;
	border: 1px solid rgba(255, 152, 0, 0.5);
	border-radius: 8px;
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
	font-size: 13px;
	cursor: pointer;
	transition: all 0.3s;
}

.change-btn:hover {
	background: rgba(255, 152, 0, 0.2);
}

.cancel-btn {
	width: 100%;
	padding: 10px;
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 8px;
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.8);
	font-size: 13px;
	cursor: pointer;
	transition: all 0.3s;
	margin-top: 10px;
}

.verification-group {
	display: flex;
	gap: 10px;
	align-items: center;
}

.verification-input {
	flex: 1;
	max-width: 140px;
}

.send-code-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 4px;
	padding: 11px 14px;
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 8px;
	background: rgba(255, 255, 255, 0.1);
	color: white;
	font-size: 12px;
	white-space: nowrap;
	cursor: pointer;
	transition: all 0.3s;
	backdrop-filter: blur(10px);
	min-width: 100px;
	height: 48px;
}

.send-code-btn:hover:not(:disabled) {
	border-color: rgba(255, 255, 255, 0.5);
	background: rgba(255, 255, 255, 0.2);
}

.send-code-btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.action-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	width: 100%;
	padding: 15px;
	border: none;
	border-radius: 12px;
	background: linear-gradient(45deg, #ff6b6b, #feca57);
	color: white;
	font-size: 15px;
	font-weight: bold;
	cursor: pointer;
	transition: all 0.3s;
	margin-top: 15px;
	box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.action-btn:hover:not(:disabled) {
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.action-btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
	transform: none;
}

.password-btn {
	background: linear-gradient(45deg, #667eea, #764ba2);
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.password-btn:hover:not(:disabled) {
	box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.contact-btn {
	background: linear-gradient(45deg, #4ecdc4, #44a08d);
	box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.contact-btn:hover:not(:disabled) {
	box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.btn-icon {
	font-size: 16px;
}

.send-code-btn .btn-icon {
	font-size: 14px;
}

/* 提示信息框样式 */
.message-box {
	display: flex;
	align-items: center;
	padding: 12px 16px;
	margin: 16px 0;
	border-radius: 8px;
	font-size: 14px;
	line-height: 1.4;
	animation: slideIn 0.3s ease-out;
}

.message-box.success {
	background: linear-gradient(135deg, #d4edda, #c3e6cb);
	border: 1px solid #b8dacc;
	color: #155724;
}

.message-box.error {
	background: linear-gradient(135deg, #f8d7da, #f1b0b7);
	border: 1px solid #f1b0b7;
	color: #721c24;
}

.message-icon {
	font-size: 16px;
	margin-right: 8px;
	flex-shrink: 0;
}

.message-text {
	flex: 1;
	font-weight: 500;
}

@keyframes slideIn {
	from {
		opacity: 0;
		transform: translateY(-10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}
</style>
