<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>套餐明细修复测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .test-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
        }

        .test-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        /* 套餐明细弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 20px;
            width: 480px;
            max-width: 95%;
            min-height: 650px;
            max-height: 90vh;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
            overflow: hidden;
            position: relative;
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24px 24px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        }

        .modal-title {
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .close-button {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            padding: 20px;
            max-height: 550px;
            overflow-y: auto;
        }

        .package-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 18px;
            margin-bottom: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .package-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .package-name {
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        .package-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .package-status.active {
            background: rgba(52, 199, 89, 0.2);
            color: #34c759;
        }

        .package-status.expired {
            background: rgba(255, 59, 48, 0.2);
            color: #ff3b30;
        }

        .package-details {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .package-detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            padding: 4px 0;
        }

        .detail-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            flex-shrink: 0;
            min-width: 90px;
        }

        .detail-value {
            color: white;
            font-size: 14px;
            font-weight: 500;
            text-align: right;
            word-break: break-word;
            flex: 1;
            margin-left: 12px;
        }

        .remaining-days {
            color: #34c759;
            font-weight: bold;
        }

        .order-no {
            font-family: monospace;
            color: #E3F2FD;
        }

        .package-amount {
            color: #FFD700;
            font-weight: bold;
        }

        .fix-info {
            background: rgba(52, 199, 89, 0.1);
            border: 1px solid rgba(52, 199, 89, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .fix-info h3 {
            color: #34c759;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .fix-info ul {
            color: #333;
            padding-left: 20px;
        }

        .fix-info li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">套餐明细修复测试</h1>
        
        <div class="fix-info">
            <h3>🔧 修复内容</h3>
            <ul>
                <li>✅ 修复开通时间和到期时间不显示的问题</li>
                <li>✅ 修复数据字段名不一致问题（packageInfo vs agentPackage）</li>
                <li>✅ 优化剩余天数计算精度</li>
                <li>✅ 增加弹窗宽度从320px到480px</li>
                <li>✅ 增加弹窗高度从500px到650px</li>
                <li>✅ 优化内容区域布局和间距</li>
                <li>✅ 增加调试日志便于排查问题</li>
            </ul>
        </div>

        <button class="test-button" onclick="showPackageModal()">
            查看套餐明细
        </button>
    </div>

    <!-- 套餐明细弹窗 -->
    <div class="modal-overlay" id="packageModal">
        <div class="modal-card">
            <div class="modal-header">
                <div class="modal-title">套餐明细</div>
                <button class="close-button" onclick="closePackageModal()">✕</button>
            </div>
            <div class="modal-content">
                <!-- 使用中的套餐 -->
                <div class="package-item">
                    <div class="package-header">
                        <div class="package-name">VIP专业版套餐</div>
                        <div class="package-status active">使用中</div>
                    </div>
                    <div class="package-details">
                        <div class="package-detail-row">
                            <div class="detail-label">订单号：</div>
                            <div class="detail-value order-no">ORD20250130001</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">购买金额：</div>
                            <div class="detail-value package-amount">¥99.00</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">开通时间：</div>
                            <div class="detail-value" id="activatedTime">2025-07-30 15:38:00</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">到期时间：</div>
                            <div class="detail-value" id="expiredTime">2025-08-30 15:38:00</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">总点数：</div>
                            <div class="detail-value">10000点</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">每日限制：</div>
                            <div class="detail-value">500点</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">剩余天数：</div>
                            <div class="detail-value remaining-days" id="remainingDays">计算中...</div>
                        </div>
                    </div>
                </div>

                <!-- 已过期的套餐 -->
                <div class="package-item">
                    <div class="package-header">
                        <div class="package-name">标准版套餐</div>
                        <div class="package-status expired">已过期</div>
                    </div>
                    <div class="package-details">
                        <div class="package-detail-row">
                            <div class="detail-label">订单号：</div>
                            <div class="detail-value order-no">ORD20241215002</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">购买金额：</div>
                            <div class="detail-value package-amount">¥59.00</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">开通时间：</div>
                            <div class="detail-value">2024-12-15 10:20:15</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">到期时间：</div>
                            <div class="detail-value">2025-01-14 10:20:15</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">总点数：</div>
                            <div class="detail-value">0点</div>
                        </div>
                        <div class="package-detail-row">
                            <div class="detail-label">每日限制：</div>
                            <div class="detail-value">0点</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPackageModal() {
            document.getElementById('packageModal').classList.add('show');
        }

        function closePackageModal() {
            document.getElementById('packageModal').classList.remove('show');
        }

        // 计算剩余天数的函数（与UniApp中的逻辑一致）
        function calculateRemainingDays() {
            // 使用与用户截图中相同的时间
            const expiredTimeStr = "2025-08-30 15:38:00";
            const endDate = new Date(expiredTimeStr);

            // 按24小时精确计算剩余时间
            const now = new Date();
            const timeDiff = endDate.getTime() - now.getTime();

            // 计算剩余天数（按24小时计算，精确到小数）
            const remainingDays = Math.max(0, Math.ceil(timeDiff / (24 * 60 * 60 * 1000)));

            // 更新显示
            document.getElementById('remainingDays').textContent = remainingDays + '天';

            // 显示计算详情
            const hoursRemaining = (timeDiff / (60 * 60 * 1000)).toFixed(2);
            const exactDays = (timeDiff / (24 * 60 * 60 * 1000)).toFixed(2);

            console.log('剩余天数计算详情（24小时精确计算）:');
            console.log('当前时间:', now.toLocaleString('zh-CN'));
            console.log('到期时间:', endDate.toLocaleString('zh-CN'));
            console.log('时间差(毫秒):', timeDiff);
            console.log('时间差(小时):', hoursRemaining);
            console.log('精确天数:', exactDays);
            console.log('剩余天数(向上取整):', remainingDays);
        }

        // 页面加载时计算剩余天数并显示弹窗
        window.onload = function() {
            calculateRemainingDays();
            showPackageModal();
        }

        // 点击遮罩层关闭弹窗
        document.getElementById('packageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePackageModal();
            }
        });
    </script>
</body>
</html>
