const mysql = require('mysql2/promise');

async function testAgentConsumptionLogic() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });
    
    console.log('✅ 数据库连接成功\n');
    
    // 测试智能体消耗点数获取逻辑
    const botId = '7491537356286459913'; // 养生计划图文智能体ID
    
    console.log('=== 测试智能体消耗点数获取逻辑 ===');
    console.log(`测试智能体ID: ${botId}\n`);
    
    // 1. 模拟主聊天函数中的消耗点数获取逻辑
    console.log('1. 主聊天函数中的消耗点数获取:');
    let pointsToDeduct = 500; // 默认值
    try {
      const agentThemeResult = await connection.execute(`
        SELECT consumption FROM agent_theme WHERE agentId = ? LIMIT 1
      `, [botId]);

      if (agentThemeResult[0] && agentThemeResult[0].length > 0 && agentThemeResult[0][0].consumption) {
        pointsToDeduct = agentThemeResult[0][0].consumption;
        console.log('✅ 从数据库获取智能体消耗点数:', pointsToDeduct);
      } else {
        console.log('⚠️ 未找到智能体配置，使用默认消耗点数:', pointsToDeduct);
      }
    } catch (error) {
      console.error('❌ 获取智能体消耗点数失败，使用默认值:', error.message);
    }
    
    // 2. 模拟saveChatToTaskTable函数中的消耗点数处理逻辑
    console.log('\n2. saveChatToTaskTable函数中的消耗点数处理:');
    const actualConsumption = pointsToDeduct; // 传递进来的参数
    let agentConsumption = actualConsumption || 500;
    
    try {
      // 从 agent_theme 表中查找匹配的智能体信息
      const agentThemeResult = await connection.execute(`
        SELECT title, icon, consumption, agentId, workflowId
        FROM agent_theme
        WHERE agentId = ? OR workflowId = ?
        ORDER BY id DESC
        LIMIT 1
      `, [botId, botId]);

      if (agentThemeResult[0] && agentThemeResult[0].length > 0) {
        const theme = agentThemeResult[0][0];
        
        // 如果没有传递消耗点数参数，则从数据库获取
        if (!actualConsumption) {
          agentConsumption = theme.consumption || 500;
        }
        
        console.log(`📋 智能体信息: ${theme.title}, 使用消耗点数: ${agentConsumption}`);
      } else {
        console.log(`⚠️ 未在数据库中找到智能体 ${botId} 的信息，使用默认值`);
      }
    } catch (error) {
      console.error('❌ 获取智能体信息失败:', error.message);
    }
    
    // 3. 模拟构建consumption对象
    console.log('\n3. 构建consumption对象:');
    const consumptionObject = {
      tokens: agentConsumption,
      inputToken: 10, // 模拟输入token数
      outputToken: 200, // 模拟输出token数
      botId: botId,
      agentName: '养生计划图文',
      agentAvatar: '/static/images/znt_avatar.png'
    };
    
    console.log('消耗对象:', JSON.stringify(consumptionObject, null, 2));
    
    // 4. 验证结果
    console.log('\n=== 验证结果 ===');
    if (consumptionObject.tokens === 9) {
      console.log('🎉 修复成功！智能体使用了正确的消耗点数 (9点)');
    } else if (consumptionObject.tokens === 500) {
      console.log('❌ 修复失败！智能体仍在使用默认的500点');
    } else {
      console.log(`⚠️ 智能体使用了 ${consumptionObject.tokens} 点，请检查配置`);
    }
    
    // 5. 检查数据库中的配置
    console.log('\n=== 数据库配置检查 ===');
    const [configs] = await connection.execute(`
      SELECT id, title, type, consumption, agentId, workflowId, enabled
      FROM agent_theme 
      WHERE agentId = ? OR workflowId = ?
      ORDER BY id DESC
    `, [botId, botId]);
    
    if (configs.length > 0) {
      configs.forEach(config => {
        console.log(`配置ID: ${config.id}, 标题: ${config.title}, 类型: ${config.type}`);
        console.log(`消耗: ${config.consumption} 点, 启用: ${config.enabled ? '是' : '否'}`);
        console.log(`AgentID: ${config.agentId || '无'}, WorkflowID: ${config.workflowId || '无'}`);
        console.log('---');
      });
    } else {
      console.log('❌ 未找到相关配置');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

testAgentConsumptionLogic();
