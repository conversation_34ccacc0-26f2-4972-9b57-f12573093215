<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>套餐数据调试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .debug-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
        }

        .debug-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .data-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }

        .data-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .data-value {
            color: #6c757d;
            font-family: monospace;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            word-break: break-all;
        }

        .status-correct {
            color: #28a745;
            font-weight: bold;
        }

        .status-error {
            color: #dc3545;
            font-weight: bold;
        }

        .fix-info {
            background: rgba(52, 199, 89, 0.1);
            border: 1px solid rgba(52, 199, 89, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .fix-info h3 {
            color: #34c759;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .comparison-table .before {
            background: rgba(220, 53, 69, 0.1);
        }

        .comparison-table .after {
            background: rgba(40, 167, 69, 0.1);
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="debug-title">套餐数据调试工具</h1>
        
        <div class="fix-info">
            <h3>🔧 主要修复内容</h3>
            <p>修复了套餐明细列表中开通时间、到期时间不显示和剩余天数不准确的问题。</p>
        </div>

        <div class="debug-section">
            <div class="section-title">📊 数据字段对比</div>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>数据来源</th>
                        <th>字段名</th>
                        <th>状态</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="after">
                        <td>开通套餐成功弹窗</td>
                        <td>packageDetail.activatedAt<br>packageDetail.expiredAt</td>
                        <td class="status-correct">✅ 正确</td>
                        <td>服务器直接返回，数据准确</td>
                    </tr>
                    <tr class="before">
                        <td>套餐明细列表（修复前）</td>
                        <td>order.agentPackage.validityDays</td>
                        <td class="status-error">❌ 错误</td>
                        <td>字段名不存在，导致数据获取失败</td>
                    </tr>
                    <tr class="after">
                        <td>套餐明细列表（修复后）</td>
                        <td>order.packageInfo.duration</td>
                        <td class="status-correct">✅ 正确</td>
                        <td>使用正确的字段名</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="debug-section">
            <div class="section-title">🕒 时间计算对比</div>
            
            <div class="data-item">
                <div class="data-label">修复前的计算逻辑：</div>
                <div class="data-value">
const timeDiff = endDate.getTime() - now.getTime()
const remainingDays = Math.max(0, Math.ceil(timeDiff / (24 * 60 * 60 * 1000)))
                </div>
                <p style="color: #dc3545; margin-top: 10px;">❌ 问题：没有考虑时区，可能存在1天误差</p>
            </div>

            <div class="data-item">
                <div class="data-label">修复后的计算逻辑：</div>
                <div class="data-value">
const now = new Date()
const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
const endDay = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate())
const timeDiff = endDay.getTime() - today.getTime()
const remainingDays = Math.max(0, Math.floor(timeDiff / (24 * 60 * 60 * 1000)) + 1)
                </div>
                <p style="color: #28a745; margin-top: 10px;">✅ 改进：重置到当天0点，精确计算天数</p>
            </div>
        </div>

        <div class="debug-section">
            <div class="section-title">📱 UI尺寸对比</div>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>属性</th>
                        <th>修复前</th>
                        <th>修复后</th>
                        <th>改进</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>弹窗宽度</td>
                        <td class="before">320px</td>
                        <td class="after">480px</td>
                        <td>+160px</td>
                    </tr>
                    <tr>
                        <td>弹窗高度</td>
                        <td class="before">500px</td>
                        <td class="after">650px</td>
                        <td>+150px</td>
                    </tr>
                    <tr>
                        <td>内容区域高度</td>
                        <td class="before">400px</td>
                        <td class="after">550px</td>
                        <td>+150px</td>
                    </tr>
                    <tr>
                        <td>最大高度</td>
                        <td class="before">80vh</td>
                        <td class="after">90vh</td>
                        <td>+10vh</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="debug-section">
            <div class="section-title">🔍 调试建议</div>
            
            <div class="data-item">
                <div class="data-label">1. 检查控制台日志</div>
                <p>修复后的代码增加了详细的调试日志，可以在浏览器控制台查看：</p>
                <div class="data-value">
console.log('处理订单数据:', order)
console.log('处理后的套餐数据:', result)
                </div>
            </div>

            <div class="data-item">
                <div class="data-label">2. 验证数据字段</div>
                <p>确认订单数据中包含正确的套餐信息字段：</p>
                <div class="data-value">
order.packageInfo.duration  // 套餐有效期（天数）
order.packageInfo.totalQuota  // 总点数
order.packageInfo.dailyMaxConsumption  // 每日限制
                </div>
            </div>

            <div class="data-item">
                <div class="data-label">3. 测试时间计算</div>
                <p>可以通过修改测试数据验证时间计算的准确性：</p>
                <div class="data-value">
// 测试当天到期的套餐
const endDate = new Date()  // 今天到期，应显示剩余1天

// 测试昨天到期的套餐  
const endDate = new Date(Date.now() - 24 * 60 * 60 * 1000)  // 应显示已过期
                </div>
            </div>
        </div>
    </div>
</body>
</html>
