<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单海报测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f9f9f9;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-image {
            max-width: 300px;
            border: 2px solid #ddd;
            margin: 10px 0;
            display: block;
        }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>简单海报测试</h1>
    
    <div class="test-section">
        <h2>1. 测试推广配置API</h2>
        <button onclick="testAPI()">获取推广配置</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试图片显示</h2>
        <button onclick="testImages()">测试图片</button>
        <div id="image-result"></div>
    </div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="info">测试中...</div>';
            
            try {
                const response = await fetch('/api/config/referral');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const config = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">✓ API调用成功</div>
                        <div class="info">海报背景图片: ${config.poster_background_image}</div>
                        <pre>${JSON.stringify(config, null, 2)}</pre>
                    `;
                    
                    // 自动测试图片
                    testImageDisplay(config.poster_background_image);
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ API调用失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ 请求失败: ${error.message}</div>`;
            }
        }

        async function testImageDisplay(imagePath) {
            const imageDiv = document.getElementById('image-result');
            
            if (!imagePath) {
                imageDiv.innerHTML = '<div class="error">✗ 没有图片路径</div>';
                return;
            }
            
            // 测试不同的URL构造方式
            const testUrls = [
                imagePath, // 原始路径
                `http://localhost:5173${imagePath}`, // 5173端口（图片服务器）
                `http://localhost:3030${imagePath}`, // 3030端口（后端服务器）
            ];
            
            let results = '<h3>图片测试结果:</h3>';
            
            for (const url of testUrls) {
                try {
                    const img = new Image();
                    const loadPromise = new Promise((resolve, reject) => {
                        img.onload = () => resolve(true);
                        img.onerror = () => reject(new Error('加载失败'));
                        setTimeout(() => reject(new Error('超时')), 3000);
                    });
                    
                    img.src = url;
                    await loadPromise;
                    
                    results += `
                        <div class="success">✓ ${url}</div>
                        <img src="${url}" class="test-image" alt="测试图片">
                    `;
                } catch (error) {
                    results += `<div class="error">✗ ${url} - ${error.message}</div>`;
                }
            }
            
            imageDiv.innerHTML = results;
        }

        function testImages() {
            // 测试一些常见的图片路径
            const testPaths = [
                '/images/banners/banner1.jpg',
                '/images/images/20250711/image_1752246578577.png',
                '/images/images/20250711/image_1752246.jpg'
            ];
            
            let results = '<h3>预设图片测试:</h3>';
            
            testPaths.forEach(path => {
                const url5173 = `http://localhost:5173${path}`;
                const url3030 = `http://localhost:3030${path}`;

                results += `
                    <div class="info">测试路径: ${path}</div>
                    <div>5173端口（图片服务）: <img src="${url5173}" class="test-image" onerror="this.style.border='2px solid red'" onload="this.style.border='2px solid green'"></div>
                    <div>3030端口（后端服务）: <img src="${url3030}" class="test-image" onerror="this.style.border='2px solid red'" onload="this.style.border='2px solid green'"></div>
                    <hr>
                `;
            });
            
            document.getElementById('image-result').innerHTML = results;
        }

        // 页面加载时自动测试API
        window.onload = function() {
            testAPI();
        };
    </script>
</body>
</html>
