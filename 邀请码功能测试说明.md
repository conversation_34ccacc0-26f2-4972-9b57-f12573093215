# 邀请码功能测试说明

## 功能概述

已成功实现邀请码功能，包括：

1. **注册页面邀请码输入框**：选填，支持手动输入和自动填入
2. **邀请链接自动绑定**：通过邀请链接注册时自动填入邀请码并锁定
3. **上下级关系建立**：注册时自动建立邀请人和被邀请人的关系
4. **邀请奖励机制**：邀请成功后给邀请人奖励积分

## 前端修改

### 1. 注册页面 (`uniapp/pages/auth/register.vue`)

#### 新增功能：
- 添加邀请码输入框（🎫图标）
- 支持邀请码锁定状态（通过邀请链接进入时）
- 添加`onLoad`方法处理URL参数
- 修改注册API调用，包含邀请码参数

#### 新增数据字段：
```javascript
registerForm: {
    // ... 原有字段
    inviteCode: ''  // 邀请码
},
inviteCodeLocked: false,  // 邀请码是否锁定
inviterInfo: null         // 邀请人信息
```

#### URL参数处理：
- `inviteCode`: 邀请码
- `inviter`: 邀请人ID（可选）

## 后端修改

### 1. 会员控制器 (`server/src/controller/MemberController.ts`)

#### 注册方法增强：
- 支持接收`inviteCode`参数
- 邀请码格式验证：`INV{userId}{timestamp}`
- 查找邀请人并验证邀请码有效性
- 建立上下级关系（设置`referrerId`）
- 邀请奖励机制（给邀请人100积分）

#### 邀请码验证逻辑：
```javascript
const inviteCodePattern = /^INV(\d+)\d{6}$/;
const match = inviteCode.match(inviteCodePattern);
if (match) {
    const inviterId = parseInt(match[1]);
    // 查找邀请人...
}
```

## 测试步骤

### 1. 准备测试环境

1. 确保后端服务运行正常
2. 确保前端UniApp项目运行正常
3. 准备一个已注册的用户账号作为邀请人

### 2. 测试邀请链接生成

1. 登录已有用户账号
2. 进入个人资料页面
3. 点击"生成海报"或查看邀请信息
4. 复制邀请链接，格式应为：
   ```
   http://localhost:8080/#/pages/auth/register?inviteCode=INV123456789&inviter=123
   ```

### 3. 测试通过邀请链接注册

#### 3.1 注册页面测试
1. 在新的浏览器窗口或无痕模式下打开邀请链接
2. 应该自动跳转到注册页面
3. 检查邀请码输入框：
   - 应该自动填入邀请码
   - 输入框应该被锁定（灰色背景，🔒图标）
   - 显示提示文字："通过邀请链接注册，邀请码已自动填入"

#### 3.2 弹窗注册测试
1. 访问带有邀请码的首页链接：`http://localhost:8080/#/?inviteCode=INV123456789`
2. 应该自动打开注册弹窗
3. 检查邀请码输入框：
   - 应该自动填入邀请码
   - 输入框可编辑（显示"邀请码（非必填）"）

### 4. 测试注册流程

1. 填写注册表单的其他必填字段：
   - 联系方式（手机号或邮箱）
   - 验证码
   - 密码
   - 确认密码
2. 勾选用户协议
3. 点击注册按钮
4. 检查注册是否成功

### 5. 测试手动输入邀请码

1. 直接访问注册页面（不通过邀请链接）
2. 邀请码输入框应该为空且可编辑
3. 手动输入有效的邀请码
4. 完成注册流程

### 6. 验证后端数据

注册成功后，检查数据库：

1. **新用户记录**：
   - `referrerId`字段应该设置为邀请人的ID
   - 其他字段正常

2. **邀请人积分**：
   - 邀请人的`points`字段应该增加100
   - `points_records`表应该有邀请奖励记录

3. **积分记录**：
   ```sql
   SELECT * FROM points_records WHERE type = 'invite_reward' ORDER BY createdAt DESC LIMIT 5;
   ```

## 错误处理

### 1. 无效邀请码
- 前端显示："邀请码无效"
- 注册失败

### 2. 邀请码格式错误
- 前端显示："邀请码格式不正确"
- 注册失败

### 3. 邀请人不存在
- 前端显示："邀请码无效"
- 注册失败

## 注意事项

1. 邀请码格式：`INV{用户ID}{6位时间戳}`
2. 邀请奖励：每次成功邀请奖励100积分
3. 上下级关系：使用`referrerId`字段存储
4. 邀请码输入框为选填，不影响正常注册流程
5. 通过邀请链接注册时，邀请码自动锁定不可修改

## 后续优化建议

1. 添加邀请码有效期限制
2. 添加邀请次数限制
3. 支持多级邀请奖励
4. 添加邀请统计页面
5. 支持自定义邀请奖励金额
