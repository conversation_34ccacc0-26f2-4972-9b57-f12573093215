<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广海报配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-image {
            max-width: 300px;
            max-height: 300px;
            border: 2px solid #ddd;
            margin: 10px;
            display: block;
        }
        .error {
            color: red;
            font-weight: bold;
            margin: 5px 0;
        }
        .success {
            color: green;
            font-weight: bold;
            margin: 5px 0;
        }
        .info {
            color: blue;
            margin: 5px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>推广海报配置测试</h1>
    
    <div class="test-section">
        <h2>1. 推广配置API测试</h2>
        <button onclick="testReferralConfig()">测试推广配置API</button>
        <div id="config-test"></div>
    </div>

    <div class="test-section">
        <h2>2. 直接测试后台设置的图片</h2>
        <button onclick="testBackendImage()">测试后台图片</button>
        <div id="backend-test"></div>
    </div>

    <div class="test-section">
        <h2>3. 诊断日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function testReferralConfig() {
            const testDiv = document.getElementById('config-test');
            testDiv.innerHTML = '<div class="info">测试中...</div>';
            
            try {
                log('开始测试推广配置API...');
                const response = await fetch('/api/config/referral');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const config = data.data;
                    log('推广配置API调用成功');
                    log('配置数据: ' + JSON.stringify(config, null, 2));
                    
                    let configResults = `
                        <div class="success">✓ 推广配置API调用成功</div>
                        <div class="info"><strong>海报背景图片:</strong> ${config.poster_background_image || '未设置'}</div>
                        <div class="info"><strong>二维码尺寸:</strong> ${config.poster_qr_code_size || '未设置'}</div>
                        <div class="info"><strong>二维码位置X:</strong> ${config.poster_qr_code_position_x || '未设置'}</div>
                        <div class="info"><strong>二维码位置Y:</strong> ${config.poster_qr_code_position_y || '未设置'}</div>
                        <div class="info"><strong>分享标题:</strong> ${config.share_title || '未设置'}</div>
                        <div class="info"><strong>分享描述:</strong> ${config.share_description || '未设置'}</div>
                        <hr>
                        <div class="info"><strong>完整配置数据:</strong></div>
                        <pre>${JSON.stringify(config, null, 2)}</pre>
                    `;
                    
                    // 测试背景图片是否可访问
                    if (config.poster_background_image) {
                        log('测试背景图片: ' + config.poster_background_image);
                        try {
                            const img = new Image();
                            const loadPromise = new Promise((resolve, reject) => {
                                img.onload = () => resolve(true);
                                img.onerror = () => reject(new Error('图片加载失败'));
                                setTimeout(() => reject(new Error('加载超时')), 5000);
                            });
                            
                            img.src = config.poster_background_image;
                            await loadPromise;
                            
                            configResults += `
                                <div class="success">✓ 配置的背景图片可访问</div>
                                <img src="${config.poster_background_image}" class="test-image" alt="配置的背景图片" style="border: 2px solid green;">
                            `;
                            log(`配置的背景图片加载成功: ${config.poster_background_image}`);
                            
                        } catch (error) {
                            configResults += `<div class="error">✗ 配置的背景图片无法访问: ${error.message}</div>`;
                            log(`配置的背景图片加载失败: ${config.poster_background_image} - ${error.message}`);
                        }
                    }
                    
                    testDiv.innerHTML = configResults;
                } else {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }
            } catch (error) {
                testDiv.innerHTML = `<div class="error">✗ 推广配置API调用失败: ${error.message}</div>`;
                log(`推广配置API调用失败: ${error.message}`);
            }
        }

        async function testBackendImage() {
            const testDiv = document.getElementById('backend-test');
            testDiv.innerHTML = '<div class="info">测试中...</div>';
            
            // 根据您截图中的路径测试
            const testImagePath = '/images/images/20250711/image_1752246.jpg';
            
            try {
                log('测试后台设置的图片: ' + testImagePath);
                
                const img = new Image();
                const loadPromise = new Promise((resolve, reject) => {
                    img.onload = () => resolve(true);
                    img.onerror = () => reject(new Error('图片加载失败'));
                    setTimeout(() => reject(new Error('加载超时')), 5000);
                });
                
                img.src = testImagePath;
                await loadPromise;
                
                testDiv.innerHTML = `
                    <div class="success">✓ 后台设置的图片可访问</div>
                    <div class="info">图片路径: ${testImagePath}</div>
                    <img src="${testImagePath}" class="test-image" alt="后台设置的图片" style="border: 2px solid green;">
                `;
                log(`后台图片加载成功: ${testImagePath}`);
                
            } catch (error) {
                testDiv.innerHTML = `
                    <div class="error">✗ 后台设置的图片无法访问: ${error.message}</div>
                    <div class="info">尝试的路径: ${testImagePath}</div>
                `;
                log(`后台图片加载失败: ${testImagePath} - ${error.message}`);
            }
        }

        // 页面加载时自动开始测试
        window.onload = function() {
            log('页面加载完成，开始测试推广配置...');
            testReferralConfig();
        };
    </script>
</body>
</html>
