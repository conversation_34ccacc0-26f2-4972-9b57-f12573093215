<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>剩余天数计算器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .calculator-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
        }

        .calculator-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .input-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
            display: block;
        }

        .input-field {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 16px;
        }

        .calculate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }

        .calculate-btn:hover {
            opacity: 0.9;
        }

        .result-section {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .result-title {
            font-size: 18px;
            font-weight: bold;
            color: #155724;
            margin-bottom: 15px;
        }

        .result-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #28a745;
        }

        .result-label {
            font-weight: bold;
            color: #495057;
        }

        .result-value {
            color: #28a745;
            font-family: monospace;
            font-size: 16px;
        }

        .calculation-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .steps-title {
            font-size: 18px;
            font-weight: bold;
            color: #856404;
            margin-bottom: 15px;
        }

        .step-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #ffc107;
        }

        .step-code {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            margin-top: 5px;
            word-break: break-all;
        }

        .current-time {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .time-display {
            font-size: 18px;
            font-weight: bold;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="calculator-container">
        <h1 class="calculator-title">剩余天数计算器</h1>
        
        <div class="current-time">
            <div class="time-display" id="currentTime">当前时间加载中...</div>
        </div>

        <div class="input-section">
            <div class="input-group">
                <label class="input-label">套餐到期时间：</label>
                <input type="datetime-local" class="input-field" id="expiredTime" value="2025-08-30T15:38">
            </div>
            <button class="calculate-btn" onclick="calculateDays()">计算剩余天数</button>
        </div>

        <div class="result-section" id="resultSection" style="display: none;">
            <div class="result-title">📊 计算结果</div>
            <div class="result-item">
                <div class="result-label">剩余天数：</div>
                <div class="result-value" id="remainingDaysResult">-</div>
            </div>
            <div class="result-item">
                <div class="result-label">套餐状态：</div>
                <div class="result-value" id="packageStatus">-</div>
            </div>
        </div>

        <div class="calculation-steps" id="stepsSection" style="display: none;">
            <div class="steps-title">🔍 计算步骤</div>
            <div class="step-item">
                <div><strong>步骤1：</strong>获取当前时间</div>
                <div class="step-code" id="step1">-</div>
            </div>
            <div class="step-item">
                <div><strong>步骤2：</strong>获取到期时间</div>
                <div class="step-code" id="step2">-</div>
            </div>
            <div class="step-item">
                <div><strong>步骤3：</strong>计算时间差（毫秒和小时）</div>
                <div class="step-code" id="step3">-</div>
            </div>
            <div class="step-item">
                <div><strong>步骤4：</strong>按24小时转换为天数（向上取整）</div>
                <div class="step-code" id="step4">-</div>
            </div>
            <div class="step-item">
                <div><strong>步骤5：</strong>确保不为负数</div>
                <div class="step-code" id="step5">-</div>
            </div>
        </div>
    </div>

    <script>
        // 更新当前时间显示
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = 
                '当前时间：' + now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
        }

        // 计算剩余天数
        function calculateDays() {
            const expiredTimeInput = document.getElementById('expiredTime').value;
            if (!expiredTimeInput) {
                alert('请选择到期时间');
                return;
            }

            const endDate = new Date(expiredTimeInput);

            // 按24小时精确计算剩余时间
            const now = new Date();
            const timeDiff = endDate.getTime() - now.getTime();

            // 计算剩余天数（按24小时计算，精确到小数）
            const remainingDays = Math.max(0, Math.ceil(timeDiff / (24 * 60 * 60 * 1000)));
            
            // 显示结果
            document.getElementById('remainingDaysResult').textContent = remainingDays + '天';
            document.getElementById('packageStatus').textContent = remainingDays > 0 ? '使用中' : '已过期';
            
            // 显示计算步骤
            const hoursRemaining = (timeDiff / (60 * 60 * 1000)).toFixed(2);
            const exactDays = (timeDiff / (24 * 60 * 60 * 1000)).toFixed(2);

            document.getElementById('step1').textContent =
                `当前时间: ${now.toLocaleString('zh-CN')}`;

            document.getElementById('step2').textContent =
                `到期时间: ${endDate.toLocaleString('zh-CN')}`;

            document.getElementById('step3').textContent =
                `时间差 = ${endDate.getTime()} - ${now.getTime()} = ${timeDiff} 毫秒\n剩余小时 = ${hoursRemaining} 小时`;

            document.getElementById('step4').textContent =
                `精确天数 = ${timeDiff} / (24 * 60 * 60 * 1000) = ${exactDays} 天\n向上取整 = Math.ceil(${exactDays}) = ${Math.ceil(parseFloat(exactDays))}`;

            document.getElementById('step5').textContent =
                `剩余天数 = Math.max(0, ${Math.ceil(parseFloat(exactDays))}) = ${remainingDays}`;
            
            // 显示结果和步骤区域
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('stepsSection').style.display = 'block';
        }

        // 页面加载时更新当前时间并自动计算
        window.onload = function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000); // 每秒更新时间
            calculateDays(); // 自动计算一次
        }
    </script>
</body>
</html>
