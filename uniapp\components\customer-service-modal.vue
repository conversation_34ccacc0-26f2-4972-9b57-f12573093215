<template>
	<view v-if="visible" class="modal-overlay" @click="closeModal">
		<view class="modal-card" @click.stop>
			<view class="modal-header">
				<text class="modal-title">联系客服</text>
				<view class="modal-close" @click="closeModal">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<view class="modal-content">
				<!-- 客服图片展示 -->
				<view v-if="customerServiceImage" class="service-image-container">
					<image
						:src="customerServiceImage"
						class="service-image"
						mode="aspectFit"
						@error="handleImageError"
					/>
					<text class="service-tip">扫描二维码联系客服</text>
				</view>

				<!-- 无客服图片时的提示 -->
				<view v-else class="no-service-container">
					<view class="no-service-icon">
						<text class="icon-emoji">💬</text>
					</view>
					<text class="no-service-text">暂未配置客服联系方式</text>
					<text class="no-service-desc">请联系管理员配置客服信息</text>
				</view>


			</view>
		</view>
	</view>
</template>

<script>
import { request } from '@/utils/request.js'

export default {
	name: 'CustomerServiceModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			customerServiceImage: '',
			loading: false
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.loadCustomerServiceConfig()
			}
		}
	},
	methods: {
		// 加载客服配置
		async loadCustomerServiceConfig() {
			try {
				this.loading = true
				console.log('开始加载客服配置...')

				// 获取system-logo配置
				const response = await request({
					url: '/api/config/system-logo',
					method: 'GET',
					loading: false
				})
				console.log('客服配置完整响应:', JSON.stringify(response, null, 2))

				if (response && response.code === 200 && response.data) {
					console.log('响应数据:', response.data)
					if (response.data.configValue) {
						const configValue = response.data.configValue
						console.log('配置值:', configValue)
						let imageUrl = configValue.customerServiceImage || ''

						// 处理图片URL，如果是相对路径，添加图片服务器地址
						if (imageUrl && !imageUrl.startsWith('http')) {
							imageUrl = 'http://localhost:5173' + imageUrl
						}

						this.customerServiceImage = imageUrl
						console.log('客服图片URL:', this.customerServiceImage)

						if (this.customerServiceImage) {
							console.log('✅ 成功获取客服图片:', this.customerServiceImage)
						} else {
							console.log('⚠️ 客服图片为空')
						}
					} else {
						console.log('⚠️ 响应中没有configValue字段')
					}
				} else {
					console.log('⚠️ 响应格式不正确或状态码不是200')
				}
			} catch (error) {
				console.error('加载客服配置失败:', error)
				uni.showToast({
					title: '加载客服信息失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		// 关闭弹窗
		closeModal() {
			this.$emit('close')
		},



		// 处理图片加载错误
		handleImageError() {
			console.error('客服图片加载失败')
			this.customerServiceImage = ''
		}
	}
}
</script>

<style scoped>
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
	backdrop-filter: blur(4px);
}

.modal-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20px;
	width: 90%;
	max-width: 400px;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: translateY(-50px) scale(0.9);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 24px;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
}

.modal-title {
	font-size: 18px;
	font-weight: 600;
	color: white;
}

.modal-close {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	justify-content: center;
	align-items: center;
	transition: all 0.2s ease;
}

.modal-close:active {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}

.close-icon {
	color: white;
	font-size: 16px;
	font-weight: bold;
}

.modal-content {
	padding: 24px;
	background: white;
	border-radius: 0 0 20px 20px;
}

.service-image-container {
	text-align: center;
	margin-bottom: 24px;
}

.service-image {
	width: 200px;
	height: 200px;
	border-radius: 12px;
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
	margin-bottom: 16px;
	transition: transform 0.2s ease;
}

.service-image:active {
	transform: scale(0.98);
}

.service-tip {
	display: block;
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}

.no-service-container {
	text-align: center;
	padding: 40px 20px;
}

.no-service-icon {
	margin-bottom: 16px;
}

.icon-emoji {
	font-size: 48px;
}

.no-service-text {
	display: block;
	font-size: 16px;
	color: #333;
	margin-bottom: 8px;
	font-weight: 500;
}

.no-service-desc {
	display: block;
	font-size: 14px;
	color: #999;
}


</style>
