<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简洁版套餐明细弹窗</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-button {
            background: white;
            color: #4f46e5;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        /* 套餐明细弹窗 - 简洁版样式 */
        .package-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            padding: 0;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .package-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .package-modal {
            background: #ffffff;
            border-radius: 10px;
            width: 300px;
            max-width: 90vw;
            max-height: 70vh;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            transform: translateY(25px);
            transition: all 0.3s ease-out;
            margin: 0 auto;
            position: relative;
        }

        .package-modal-overlay.show .package-modal {
            transform: translateY(0);
        }

        /* 头部样式 */
        .package-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .package-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }

        .close-btn {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            color: white;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .close-icon {
            font-size: 18px;
            font-weight: 300;
        }

        /* 内容区域 */
        .package-content {
            max-height: calc(70vh - 60px);
            padding: 10px;
            overflow-y: auto;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        /* 加载和空状态 */
        .loading-state, .empty-state {
            text-align: center;
            padding: 40px 20px;
        }

        .loading-text, .empty-text {
            font-size: 14px;
            color: #999;
        }

        /* 套餐项列表 */
        .package-items {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .package-item {
            background: #fafafa;
            border-radius: 6px;
            padding: 8px;
            border: 1px solid #f0f0f0;
            transition: all 0.2s ease;
            width: 100%;
            box-sizing: border-box;
        }

        .package-item:hover {
            background: #f5f5f5;
        }

        /* 套餐项头部 - 重新设计布局 */
        .item-header {
            display: flex;
            flex-direction: column;
            width: 100%;
            gap: 6px;
        }

        /* 第一行：套餐名称和价格 */
        .item-left {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            width: 100%;
            margin-bottom: 4px;
        }

        .item-name {
            font-size: 12px;
            font-weight: 600;
            color: #333;
            flex: 1;
            margin-right: 8px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .item-amount {
            font-size: 13px;
            font-weight: 700;
            color: #4f46e5;
            white-space: nowrap;
            flex-shrink: 0;
        }

        /* 第二行：订单号和状态 */
        .item-right {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .item-order {
            font-size: 10px;
            color: #999;
            flex: 1;
            margin-right: 8px;
        }

        .item-status {
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 9px;
            flex-shrink: 0;
            max-width: 60px;
            text-align: center;
        }

        .status-active {
            background: #dcfce7;
            color: #16a34a;
        }

        .status-expired {
            background: #fef2f2;
            color: #dc2626;
        }

        .status-text {
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 套餐详情 */
        .item-details {
            border-top: 1px solid #f0f0f0;
            padding-top: 12px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
        }

        .detail-row.highlight {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 6px;
            padding: 8px 10px;
            margin: 4px 0;
            border: 1px solid #f59e0b;
        }

        .detail-label {
            font-size: 13px;
            color: #666;
        }

        .detail-value {
            font-size: 13px;
            color: #333;
            font-weight: 500;
        }

        .detail-value.remaining {
            color: #f59e0b;
            font-weight: 700;
            font-size: 14px;
        }

        /* 响应式 */
        @media (max-width: 480px) {
            .package-modal {
                width: 280px;
                max-width: 85vw;
            }

            .package-modal-overlay {
                padding: 20px;
            }

            .package-content {
                padding: 8px;
            }

            .package-item {
                padding: 6px;
            }
        }
    </style>
</head>
<body>
    <button class="demo-button" onclick="showPackageModal()">
        📦 查看简洁版套餐明细
    </button>

    <!-- 套餐明细弹窗 - 简洁版 -->
    <div id="packageModal" class="package-modal-overlay" onclick="closePackageModal()">
        <div class="package-modal" onclick="event.stopPropagation()">
            <!-- 头部 -->
            <div class="package-header">
                <div class="package-title">套餐明细</div>
                <button class="close-btn" onclick="closePackageModal()">
                    <span class="close-icon">×</span>
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="package-content">
                <!-- 套餐列表 -->
                <div class="package-items">
                    <!-- 使用中的套餐 -->
                    <div class="package-item">
                        <!-- 套餐基本信息 - 重新设计布局 -->
                        <div class="item-header">
                            <!-- 第一行：套餐名称和价格 -->
                            <div class="item-left">
                                <div class="item-name">VIP专业版套餐</div>
                                <div class="item-amount">¥99.00</div>
                            </div>
                            <!-- 第二行：订单号和状态 -->
                            <div class="item-right">
                                <div class="item-order">ORD20250730001</div>
                                <div class="item-status status-active">
                                    <span class="status-text">使用中</span>
                                </div>
                            </div>
                        </div>

                        <!-- 套餐详情 -->
                        <div class="item-details">
                            <div class="detail-row">
                                <span class="detail-label">开通时间</span>
                                <span class="detail-value">2025-07-30 15:38</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">到期时间</span>
                                <span class="detail-value">2025-08-30 15:38</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">总点数</span>
                                <span class="detail-value">10000点</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">每日限制</span>
                                <span class="detail-value">500点</span>
                            </div>
                            <div class="detail-row highlight">
                                <span class="detail-label">剩余天数</span>
                                <span class="detail-value remaining" id="remainingDays">31天</span>
                            </div>
                        </div>
                    </div>

                    <!-- 已过期的套餐 -->
                    <div class="package-item">
                        <div class="item-header">
                            <!-- 第一行：套餐名称和价格 -->
                            <div class="item-left">
                                <div class="item-name">标准版套餐</div>
                                <div class="item-amount">¥59.00</div>
                            </div>
                            <!-- 第二行：订单号和状态 -->
                            <div class="item-right">
                                <div class="item-order">ORD20241215002</div>
                                <div class="item-status status-expired">
                                    <span class="status-text">已过期</span>
                                </div>
                            </div>
                        </div>

                        <div class="item-details">
                            <div class="detail-row">
                                <span class="detail-label">开通时间</span>
                                <span class="detail-value">2024-12-15 10:20</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">到期时间</span>
                                <span class="detail-value">2025-01-14 10:20</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">总点数</span>
                                <span class="detail-value">5000点</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">每日限制</span>
                                <span class="detail-value">不限制</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPackageModal() {
            document.getElementById('packageModal').classList.add('show');
            calculateRemainingDays();
        }

        function closePackageModal() {
            document.getElementById('packageModal').classList.remove('show');
        }

        function calculateRemainingDays() {
            const expiredTimeStr = "2025-08-30 15:38:00";
            const endDate = new Date(expiredTimeStr);
            const now = new Date();
            const timeDiff = endDate.getTime() - now.getTime();
            const remainingDays = Math.max(0, Math.ceil(timeDiff / (24 * 60 * 60 * 1000)));
            
            document.getElementById('remainingDays').textContent = remainingDays + '天';
        }

        // 页面加载时自动显示弹窗
        window.onload = function() {
            setTimeout(showPackageModal, 500);
        }
    </script>
</body>
</html>
