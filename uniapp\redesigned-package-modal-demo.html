<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重新设计的套餐弹窗演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        /* 弹窗遮罩 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* 套餐详情弹窗样式 - 重新设计版本 */
        .package-detail-modal {
            width: 680px;
            max-width: 85vw;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 32px 80px rgba(0, 0, 0, 0.12), 0 8px 32px rgba(0, 0, 0, 0.08);
            margin: 0 auto;
            position: relative;
            animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            transform: translateX(0);
        }

        @keyframes modalSlideIn {
            0% {
                opacity: 0;
                transform: scale(0.9) translateY(30px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* 弹窗头部样式 */
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 20px;
            flex: 1;
        }

        .success-badge {
            width: 64px;
            height: 64px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .success-icon {
            color: white;
            font-size: 32px;
            font-weight: bold;
        }

        .header-text {
            flex: 1;
        }

        .modal-title {
            font-size: 32px;
            font-weight: 600;
            color: white;
            display: block;
            margin-bottom: 4px;
        }

        .modal-subtitle {
            font-size: 24px;
            color: rgba(255, 255, 255, 0.8);
            display: block;
        }

        .close-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: none;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.1);
        }

        .close-icon {
            font-size: 32px;
            color: white;
            font-weight: 300;
        }

        /* 弹窗内容样式 */
        .detail-content {
            padding: 32px;
        }

        /* 套餐信息卡片 */
        .package-info-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 32px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .package-header {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .package-icon {
            font-size: 48px;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 16px;
        }

        .package-details {
            flex: 1;
        }

        .package-name {
            font-size: 32px;
            font-weight: 600;
            color: #333;
            display: block;
            margin-bottom: 8px;
        }

        .package-desc {
            font-size: 26px;
            color: #666;
            display: block;
        }

        .package-badge {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            padding: 8px 16px;
            border-radius: 20px;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .badge-text {
            color: white;
            font-size: 24px;
            font-weight: 500;
        }

        /* 详细信息部分 */
        .detail-sections {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .detail-section {
            background: white;
            border-radius: 16px;
            border: 1px solid #f0f0f0;
            overflow: hidden;
        }

        .section-title {
            background: #fafbfc;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .section-icon {
            font-size: 28px;
        }

        .section-text {
            font-size: 28px;
            font-weight: 600;
            color: #333;
        }

        .detail-grid {
            padding: 24px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-size: 28px;
            color: #666;
            font-weight: 400;
        }

        .detail-value {
            font-size: 28px;
            color: #333;
            font-weight: 500;
        }

        .detail-value.highlight {
            color: #667eea;
            font-weight: 600;
        }

        .detail-value.success {
            color: #4CAF50;
            font-weight: 600;
        }

        /* 弹窗底部 */
        .detail-footer {
            padding: 24px 32px 32px;
            display: flex;
            justify-content: center;
            background: #fafbfc;
            border-top: 1px solid #f0f0f0;
        }

        .confirm-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 48px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .confirm-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .button-text {
            position: relative;
            z-index: 2;
        }

        .button-shine {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .confirm-button:hover .button-shine {
            left: 100%;
        }

        /* 响应式适配 */
        @media screen and (max-width: 768px) {
            .package-detail-modal {
                width: 90vw;
                max-width: 640px;
                max-height: 90vh;
                margin: 0 auto;
                transform: translateX(0);
            }

            .modal-header {
                padding: 24px;
            }

            .header-content {
                gap: 16px;
            }

            .success-badge {
                width: 56px;
                height: 56px;
            }

            .success-icon {
                font-size: 28px;
            }

            .modal-title {
                font-size: 28px;
            }

            .modal-subtitle {
                font-size: 22px;
            }

            .close-btn {
                width: 48px;
                height: 48px;
            }

            .close-icon {
                font-size: 28px;
            }

            .detail-content {
                padding: 24px;
            }

            .package-info-card {
                padding: 20px;
                margin-bottom: 24px;
            }

            .package-header {
                gap: 16px;
            }

            .package-icon {
                font-size: 40px;
                width: 64px;
                height: 64px;
            }

            .package-name {
                font-size: 28px;
            }

            .package-desc {
                font-size: 24px;
            }

            .detail-sections {
                gap: 20px;
            }

            .section-title {
                padding: 16px 20px;
            }

            .section-icon {
                font-size: 24px;
            }

            .section-text {
                font-size: 26px;
            }

            .detail-grid {
                padding: 20px;
                gap: 16px;
            }

            .detail-item {
                padding: 12px 0;
            }

            .detail-label,
            .detail-value {
                font-size: 26px;
            }

            .detail-footer {
                padding: 20px 24px 24px;
            }
        }
    </style>
</head>
<body>
    <button class="demo-button" onclick="showPackageModal()">
        🎉 查看重新设计的套餐弹窗
    </button>

    <!-- 套餐详情弹窗 - 重新设计版本 -->
    <div id="packageModal" class="modal-overlay" onclick="closePackageModal()">
        <div class="package-detail-modal" onclick="event.stopPropagation()">
            <!-- 弹窗头部 -->
            <div class="modal-header">
                <div class="header-content">
                    <div class="success-badge">
                        <span class="success-icon">✓</span>
                    </div>
                    <div class="header-text">
                        <span class="modal-title">套餐激活成功</span>
                        <span class="modal-subtitle">Package Activated Successfully</span>
                    </div>
                </div>
                <button class="close-btn" onclick="closePackageModal()">
                    <span class="close-icon">×</span>
                </button>
            </div>

            <!-- 弹窗内容 -->
            <div class="detail-content">
                <!-- 套餐基本信息卡片 -->
                <div class="package-info-card">
                    <div class="package-header">
                        <div class="package-icon">📦</div>
                        <div class="package-details">
                            <span class="package-name">VIP专业版套餐</span>
                            <span class="package-desc">享受更多高级功能和优质服务</span>
                        </div>
                        <div class="package-badge">
                            <span class="badge-text">已激活</span>
                        </div>
                    </div>
                </div>

                <!-- 详细信息列表 -->
                <div class="detail-sections">
                    <!-- 时间信息 -->
                    <div class="detail-section">
                        <div class="section-title">
                            <span class="section-icon">⏰</span>
                            <span class="section-text">时间信息</span>
                        </div>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">开通时间</span>
                                <span class="detail-value">2025-01-30 14:30</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">到期时间</span>
                                <span class="detail-value">2025-02-28 14:30</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">有效期</span>
                                <span class="detail-value highlight">30 天</span>
                            </div>
                        </div>
                    </div>

                    <!-- 账户信息 -->
                    <div class="detail-section">
                        <div class="section-title">
                            <span class="section-icon">💰</span>
                            <span class="section-text">账户信息</span>
                        </div>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">获得点数</span>
                                <span class="detail-value success">+1000</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">当前余额</span>
                                <span class="detail-value">¥50.00</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">当前点数</span>
                                <span class="detail-value">1500</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 弹窗底部 -->
            <div class="detail-footer">
                <button class="confirm-button" onclick="closePackageModal()">
                    <span class="button-text">确定</span>
                    <div class="button-shine"></div>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showPackageModal() {
            const modal = document.getElementById('packageModal');
            modal.classList.add('show');
        }

        function closePackageModal() {
            const modal = document.getElementById('packageModal');
            modal.classList.remove('show');
        }

        // 按ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePackageModal();
            }
        });
    </script>
</body>
</html>
