const mysql = require('mysql2/promise');

async function testWorkflowTaskSave() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'ai_agent'
  });

  try {
    console.log('🔍 测试工作流任务保存功能...\n');

    // 1. 查看 agent_theme 表中的工作流数据
    console.log('1. 查看 agent_theme 表中的工作流数据:');
    const [themes] = await connection.execute(`
      SELECT id, workflowId, title, icon FROM agent_theme 
      WHERE workflowId IS NOT NULL AND workflowId != ''
    `);
    
    if (themes.length > 0) {
      console.log('找到的工作流主题:');
      themes.forEach(theme => {
        console.log(`  - ID: ${theme.id}, WorkflowID: ${theme.workflowId}, 标题: ${theme.title}, 图标: ${theme.icon}`);
      });
    } else {
      console.log('❌ 没有找到工作流主题数据');
      
      // 插入测试数据
      console.log('\n📝 插入测试工作流主题数据...');
      await connection.execute(`
        INSERT INTO agent_theme (workflowId, title, icon, description, createTime, updateTime) 
        VALUES (?, ?, ?, ?, NOW(), NOW())
      `, ['7491594398599184411', '测试工作流', '/static/images/test-workflow.png', '这是一个测试工作流']);
      
      console.log('✅ 测试数据插入成功');
    }

    // 2. 查看最近的任务记录
    console.log('\n2. 查看最近的任务记录:');
    const [tasks] = await connection.execute(`
      SELECT id, userId, userName, taskSource, taskType, taskTypeDetail, 
             status, logs, consumption, createTime
      FROM task 
      ORDER BY createTime DESC 
      LIMIT 5
    `);

    if (tasks.length > 0) {
      console.log('最近的任务记录:');
      tasks.forEach(task => {
        console.log(`  - ID: ${task.id}`);
        console.log(`    用户: ${task.userName} (${task.userId})`);
        console.log(`    任务来源(图标): ${task.taskSource}`);
        console.log(`    任务类型: ${task.taskType}`);
        console.log(`    任务详情(标题): ${task.taskTypeDetail}`);
        console.log(`    状态: ${task.status}`);
        console.log(`    日志(工作流名称): ${task.logs}`);
        console.log(`    消耗: ${task.consumption}`);
        console.log(`    创建时间: ${task.createTime}`);
        console.log('    ---');
      });
    } else {
      console.log('❌ 没有找到任务记录');
    }

    // 3. 模拟工作流执行后的数据保存效果
    console.log('\n3. 模拟工作流执行数据保存:');
    
    // 获取工作流信息
    const workflowId = '7491594398599184411';
    const [workflowResult] = await connection.execute(`
      SELECT icon, title FROM agent_theme WHERE workflowId = ? LIMIT 1
    `, [workflowId]);
    
    let workflowIcon = '/static/images/workflow-default.png';
    let workflowTitle = `工作流 ${workflowId}`;
    
    if (workflowResult && workflowResult.length > 0) {
      workflowIcon = workflowResult[0].icon || workflowIcon;
      workflowTitle = workflowResult[0].title || workflowTitle;
    }
    
    console.log(`工作流图标: ${workflowIcon}`);
    console.log(`工作流标题: ${workflowTitle}`);
    
    // 模拟保存任务数据
    const taskData = {
      userId: 59,
      userName: '娃哈哈1',
      taskSource: workflowIcon, // 保存工作流图标
      taskType: '工作流', // 保存 "工作流"
      taskTypeDetail: workflowTitle, // 保存工作流标题
      consumption: 10,
      status: '是否完成相关信息显示', // 保存完成状态信息
      logs: workflowTitle, // 保存当前执行工作流的名称
      input: JSON.stringify({test: 'parameter'}),
      output: JSON.stringify({result: 'success'}),
      instructions: `执行工作流: ${workflowTitle}`
    };
    
    console.log('\n📝 即将保存的任务数据:');
    console.log(`  taskSource (图标): ${taskData.taskSource}`);
    console.log(`  taskType (类型): ${taskData.taskType}`);
    console.log(`  taskTypeDetail (标题): ${taskData.taskTypeDetail}`);
    console.log(`  status (状态): ${taskData.status}`);
    console.log(`  logs (工作流名称): ${taskData.logs}`);
    
    // 实际保存到数据库
    const [insertResult] = await connection.execute(`
      INSERT INTO task (
        userId, userName, taskSource, taskType, taskTypeDetail,
        consumption, status, logs, input, output, instructions,
        createTime, updateTime
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      taskData.userId,
      taskData.userName,
      taskData.taskSource,
      taskData.taskType,
      taskData.taskTypeDetail,
      JSON.stringify(taskData.consumption),
      taskData.status,
      taskData.logs,
      taskData.input,
      taskData.output,
      taskData.instructions
    ]);
    
    console.log(`✅ 任务保存成功，任务ID: ${insertResult.insertId}`);
    
    // 4. 验证保存的数据
    console.log('\n4. 验证刚保存的数据:');
    const [savedTask] = await connection.execute(`
      SELECT * FROM task WHERE id = ?
    `, [insertResult.insertId]);
    
    if (savedTask.length > 0) {
      const task = savedTask[0];
      console.log('✅ 数据验证成功:');
      console.log(`  taskSource (应该是图标): ${task.taskSource}`);
      console.log(`  taskType (应该是"工作流"): ${task.taskType}`);
      console.log(`  taskTypeDetail (应该是工作流标题): ${task.taskTypeDetail}`);
      console.log(`  status (应该是完成状态信息): ${task.status}`);
      console.log(`  logs (应该是工作流名称): ${task.logs}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await connection.end();
  }
}

testWorkflowTaskSave();
