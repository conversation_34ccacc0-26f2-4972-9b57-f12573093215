const mysql = require('mysql2/promise');

async function checkTaskSource() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });
    
    console.log('✅ 数据库连接成功\n');
    
    // 查询所有task记录的详细信息
    console.log('🔍 查询task表中的taskSource和consumption字段:');
    const [tasks] = await connection.query(`
      SELECT id, userId, taskType, taskTypeDetail, taskSource, consumption, status, createTime
      FROM task 
      ORDER BY id DESC 
      LIMIT 10
    `);
    
    if (tasks.length === 0) {
      console.log('❌ 没有找到任何task记录');
    } else {
      console.log(`✅ 找到 ${tasks.length} 条记录:\n`);
      tasks.forEach((task, index) => {
        console.log(`${index + 1}. ID: ${task.id} | 类型: ${task.taskType}`);
        console.log(`   详情: ${task.taskTypeDetail}`);
        console.log(`   taskSource: ${task.taskSource}`);
        console.log(`   consumption: ${typeof task.consumption === 'object' ? JSON.stringify(task.consumption) : task.consumption}`);
        console.log(`   状态: ${task.status}`);
        console.log(`   创建时间: ${task.createTime}`);
        console.log('   ---\n');
      });
    }

    // 专门查询工作流记录
    console.log('🔍 专门查询工作流记录的taskSource字段:');
    const [workflowTasks] = await connection.query(`
      SELECT id, taskType, taskTypeDetail, taskSource, consumption
      FROM task 
      WHERE taskType = '工作流'
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    if (workflowTasks.length === 0) {
      console.log('❌ 没有找到工作流记录');
    } else {
      console.log(`✅ 找到 ${workflowTasks.length} 条工作流记录:\n`);
      workflowTasks.forEach((task, index) => {
        console.log(`${index + 1}. ID: ${task.id}`);
        console.log(`   taskSource (图标): ${task.taskSource}`);
        console.log(`   consumption: ${typeof task.consumption === 'object' ? JSON.stringify(task.consumption) : task.consumption}`);
        console.log('   ---\n');
      });
    }

  } catch (error) {
    console.error('❌ 数据库操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('✅ 数据库连接已关闭');
    }
  }
}

checkTaskSource();
