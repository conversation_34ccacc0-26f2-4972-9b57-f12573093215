const axios = require('axios');

async function testAgentConsumption() {
  try {
    console.log('🧪 测试智能体消耗点数修复...\n');
    
    // 模拟智能体聊天请求
    const testData = {
      bot_id: '7491537356286459913', // 养生计划图文智能体ID
      user_id: 'test_user_123',
      additional_messages: [
        {
          role: 'user',
          content: '你好，请帮我制定一个简单的养生计划',
          content_type: 'text'
        }
      ],
      stream: false
    };
    
    console.log('📤 发送智能体聊天请求...');
    console.log('智能体ID:', testData.bot_id);
    console.log('用户消息:', testData.additional_messages[0].content);
    
    // 发送请求到本地服务器
    const response = await axios.post('http://localhost:3001/api/coze-proxy/chat', testData, {
      headers: {
        'Content-Type': 'application/json',
        'x-user-token': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NTksInBob25lIjoiMTg4ODg4ODg4ODgiLCJpYXQiOjE3MjI0MzI5NzIsImV4cCI6MTcyMjUxOTM3Mn0.Ej3Ej3Ej3Ej3Ej3Ej3Ej3Ej3Ej3Ej3Ej3Ej3Ej3E' // 测试用户token
      },
      timeout: 30000
    });
    
    console.log('✅ 智能体聊天请求成功');
    console.log('响应状态:', response.status);
    
    if (response.data && response.data.messages) {
      const lastMessage = response.data.messages[response.data.messages.length - 1];
      if (lastMessage && lastMessage.content) {
        console.log('🤖 智能体回复:', lastMessage.content.substring(0, 100) + '...');
      }
    }
    
    // 等待一下让数据保存完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 查询最新的task记录，验证消耗点数
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ai_agent'
    });
    
    console.log('\n📊 查询最新的task记录...');
    const [tasks] = await connection.execute(`
      SELECT id, userName, taskType, taskTypeDetail, consumption, status, createTime 
      FROM task 
      WHERE taskType = '智能体' 
      ORDER BY id DESC 
      LIMIT 1
    `);
    
    if (tasks.length > 0) {
      const task = tasks[0];
      console.log(`✅ 找到最新的智能体任务记录:`);
      console.log(`ID: ${task.id}`);
      console.log(`用户: ${task.userName}`);
      console.log(`智能体: ${task.taskTypeDetail}`);
      
      // 解析consumption数据
      let consumptionData;
      try {
        if (typeof task.consumption === 'string') {
          consumptionData = JSON.parse(task.consumption);
        } else {
          consumptionData = task.consumption;
        }
        
        console.log(`消耗点数: ${consumptionData.tokens} 点`);
        console.log(`输入Token: ${consumptionData.inputToken}`);
        console.log(`输出Token: ${consumptionData.outputToken}`);
        console.log(`智能体ID: ${consumptionData.botId}`);
        
        // 检查是否使用了正确的消耗点数（应该是9点，不是500点）
        if (consumptionData.tokens === 9) {
          console.log('🎉 修复成功！智能体使用了正确的消耗点数 (9点)');
        } else if (consumptionData.tokens === 500) {
          console.log('❌ 修复失败！智能体仍在使用默认的500点');
        } else {
          console.log(`⚠️ 智能体使用了 ${consumptionData.tokens} 点，请检查配置`);
        }
        
      } catch (e) {
        console.log(`❌ 解析consumption数据失败:`, e.message);
        console.log(`原始数据:`, task.consumption);
      }
      
      console.log(`状态: ${task.status}`);
      console.log(`创建时间: ${task.createTime}`);
    } else {
      console.log('❌ 未找到智能体任务记录');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testAgentConsumption();
